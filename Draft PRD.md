# 1. Introduction

### Project overview
The Maintenance Management System is a Django-based web application that allows for the management, scheduling and tracking of the routine maintenance of shop machinery. The system will allow the supervisor to monitor and update routine machine maintenance and parts servicing, schedule servicing, and assign service tasks to technicians. Over time, the system will allow for analysis of the machine checks, parts usage, and provide insights into the shop processes that may need improvement.

### Target Audience
The supervisor will be the sole user of the system, and will be responsible for the data entry, monitoring, and report retrieval. The supervisor will assign service tickets to technicians, and generate service sheets with the list of machine parts that need to be serviced. The technicians will fill out the forms, add the appropriate comments and fill out the fields, and give it back to the supervisor. The supervisor may inspect the machine afterwards and add their own notes, then enter the record into the system.

### Business Objectives
The goal of the system, as mentioned above, would be to:
* Manage the routine servicing of machines
* Keep track of parts consumption and replacement over time
* Appraise the performance of technicians on their maintenance tasks
* provide schedules and reminders of the details of machine maintenance so that servicing is performed as required
* Improve machine care and prevent breakdowns as much as possible

# 2. Functional Requirements
### User Roles:
* Supervisor
* System administrator (superuser)

### Data Entities (Database Layer)
**Machines:**
* id
* machine name
* serial number
* tag number
* location

**Parts:**
- id
- part name
- description
- minimum quantity

**Part orders:**
- id
- part id
- order date
- quantity
- order reference document

**Machine Parts:** (note: parts and machines may have a many to one relationship. The same part may have different servicing frequency and servicing instructions for different machines)
- id
- machine id
- part id
- service frequency
- service instructions

**Technicians:**
- id
- name
- ... other info

**Service Schedule:**
- id
- machine part
- assigned technician (optional)
- date

**Service ticket:**
- id
- service date
- assigned technician

**Service ticket item:**
- id
- service id
- machine part
- referenced schedule (optional)

**Service item records:**
- id
- ticket id (optional)
- ticket item id (optional) (dropdown if ticket id found)
- machine (automatically filled if ticket item id found)
- part (same autofill as above)
- service date
- number of parts replaced (default=0)
- technician comments
- inspection comments

### Data Entry Forms
- **Machine entry**
- **Parts entry**
- **Machine part composition** (Add parts)
	- Note: include service instructions and frequency here. 
	- dialog form with dropdown/search to find part, textbox for instructions, number and unit (days, weeks, months, etc) for service frequency
- **Technician data entry**
- **Parts order entry**
- **Create service ticket**
	- enter date, select technician
	- "add item manually" by machine then part
	- or "choose scheduled item" button which will list existing schedule items
	- button to generate ticket sheet
- **Service record entry** (item by item)
	- ticket dropdown selection
	- ticket item (items from selected ticket)
	- machine, part and technician (automatically filled if ticket item id found)
	- or manually enter machine, part and technician if impromptu service
	- number of parts replaced
	- Supervisor comments
	- Button to schedule next service (based on service frequency)

### Dashboard Components
- Upcoming maintenance schedule (overdue items in red)
- Print report by selected time period
- Part stock
- Recent Activity

### Data Table Views
- Machines and parts lists, (with quantity on hand)
- Technician list
- Technician Job History
- Parts inventory (with "add order")
- Part use history (by date when ordered and date when consumed)
- Service history
- Service Schedule (with "Add Service Schedule")

### Data Detail Views
- Service ticket report

### Document Generation
* Service Ticket worksheet
* Parts order request
* Unserviced parts report

# 3. User Interface
Front end designed will be based on the template https://spark.bootlab.io/dashboard-default

# 4. Technology Stack
- Django
- Bootstrap 5
- Sqlite3
- Docker
- Script for updating app from GitHub