"""
Base form classes with Spark template styling for machine maintenance system.
"""
from django import forms


class SparkBaseForm(forms.Form):
    """Base form class with Spark template styling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply common styling to all fields
        for field_name, field in self.fields.items():
            self._apply_spark_styling(field)
    
    def _apply_spark_styling(self, field):
        """Apply Spark Bootstrap classes to form fields"""
        widget = field.widget
        
        # Text inputs, email, password, etc.
        if isinstance(widget, (forms.TextInput, forms.EmailInput, forms.PasswordInput, 
                              forms.NumberInput, forms.URLInput, forms.DateInput)):
            widget.attrs.update({'class': 'form-control'})
        
        # Textarea
        elif isinstance(widget, forms.Textarea):
            widget.attrs.update({'class': 'form-control', 'rows': 3})
        
        # Select dropdowns
        elif isinstance(widget, forms.Select):
            widget.attrs.update({'class': 'form-select'})
        
        # Checkboxes
        elif isinstance(widget, forms.CheckboxInput):
            widget.attrs.update({'class': 'form-check-input'})
        
        # File inputs
        elif isinstance(widget, forms.FileInput):
            widget.attrs.update({'class': 'form-control'})


class SparkModelForm(forms.ModelForm, SparkBaseForm):
    """Base ModelForm with Spark styling"""
    pass


class SparkCRUDForm(SparkModelForm):
    """Base form for CRUD operations with common patterns"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add common validation styling
        for field_name, field in self.fields.items():
            if field.required:
                field.widget.attrs['required'] = True
            
            # Add placeholder text based on field name if not already set
            if 'placeholder' not in field.widget.attrs:
                field.widget.attrs['placeholder'] = f"Enter {field.label.lower()}"


class SparkSearchForm(SparkBaseForm):
    """Base form for search/filter forms"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Make all search fields optional and add search styling
        for field_name, field in self.fields.items():
            field.required = False
            if isinstance(field.widget, forms.TextInput):
                field.widget.attrs.update({
                    'class': 'form-control',
                    'placeholder': f'Search by {field.label.lower()}...'
                })


class SparkModalForm(SparkModelForm):
    """Base form for modal dialogs with compact styling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Use smaller form controls for modals
        for field_name, field in self.fields.items():
            widget = field.widget
            if 'class' in widget.attrs:
                # Add small size class to existing form controls
                if 'form-control' in widget.attrs['class']:
                    widget.attrs['class'] += ' form-control-sm'
                elif 'form-select' in widget.attrs['class']:
                    widget.attrs['class'] += ' form-select-sm'
