"""
Form mixins for common field patterns in machine maintenance system.
"""
from django import forms


class TimestampFieldsMixin:
    """Mixin for forms that need created/updated timestamp display"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if hasattr(self, 'instance') and self.instance.pk:
            # Add read-only timestamp fields for existing objects
            self.fields['created_at'] = forms.DateTimeField(
                label='Created',
                widget=forms.DateTimeInput(attrs={
                    'class': 'form-control',
                    'readonly': True
                }),
                required=False,
                initial=getattr(self.instance, 'created_at', None)
            )
            self.fields['updated_at'] = forms.DateTimeField(
                label='Last Updated',
                widget=forms.DateTimeInput(attrs={
                    'class': 'form-control',
                    'readonly': True
                }),
                required=False,
                initial=getattr(self.instance, 'updated_at', None)
            )


class ContactFieldsMixin:
    """Mixin for forms with email/phone fields"""
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter email address'
        }),
        required=False
    )
    
    phone = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter phone number'
        }),
        required=False
    )


class LocationFieldsMixin:
    """Mixin for forms with location/address fields"""
    location = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter location/address'
        }),
        required=False
    )


class ServiceFrequencyMixin:
    """Mixin for forms that need service frequency fields"""
    FREQUENCY_UNITS = [
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]
    
    service_frequency = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter frequency number'
        }),
        label='Service Frequency'
    )
    
    service_frequency_unit = forms.ChoiceField(
        choices=FREQUENCY_UNITS,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='Frequency Unit'
    )


class CommentsFieldsMixin:
    """Mixin for forms with comment fields"""
    technician_comments = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter technician comments...'
        }),
        required=False,
        label='Technician Comments'
    )
    
    supervisor_comments = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter supervisor comments...'
        }),
        required=False,
        label='Supervisor Comments'
    )
