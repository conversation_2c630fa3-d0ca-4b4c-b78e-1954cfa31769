"""
Forms for machine management in the maintenance system.
"""
from django import forms
from .models import Machine, Part, PartOrder, MachinePart, MachineSystem
from forms.base import SparkCRUDForm, SparkSearchForm
from forms.mixins import TimestampFieldsMixin, LocationFieldsMixin, ServiceFrequencyMixin


class MachineSystemForm(TimestampFieldsMixin, SparkCRUDForm):
    """Form for creating and editing machine systems"""

    class Meta:
        model = MachineSystem
        fields = ['name', 'description']
        labels = {
            'name': 'System Name',
            'description': 'Description',
        }

    # Override description field for custom styling
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter system description'
        }),
        required=False,
        label='Description'
    )


class MachineForm(TimestampFieldsMixin, LocationFieldsMixin, SparkCRUDForm):
    """Form for creating and editing machines"""
    
    class Meta:
        model = Machine
        fields = ['system', 'name', 'serial_number', 'tag_number', 'description', 'location']
        labels = {
            'system': 'Machine System',
            'name': 'Machine Name',
            'serial_number': 'Serial Number',
            'tag_number': 'Tag Number',
            'description': 'Description',
            'location': 'Location',
        }
    
    # Override description field for custom styling
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter detailed machine description'
        }),
        required=False,
        label='Description'
    )


class PartForm(TimestampFieldsMixin, SparkCRUDForm):
    """Form for creating and editing parts"""
    
    class Meta:
        model = Part
        fields = ['part_no', 'description', 'minimum_stock']
        labels = {
            'part_no': 'Part Number',
            'description': 'Description',
            'minimum_stock': 'Minimum Stock Level',
        }
    
    # Override fields for custom styling
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter part description'
        }),
        required=False,
        label='Description'
    )
    
    minimum_stock = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter minimum stock level'
        }),
        label='Minimum Stock Level',
        help_text='Minimum quantity that should be kept in stock'
    )


class PartOrderForm(SparkCRUDForm):
    """Form for creating part orders"""
    
    class Meta:
        model = PartOrder
        fields = ['part', 'quantity', 'order_date', 'order_reference_document']
        labels = {
            'part': 'Part',
            'quantity': 'Quantity Ordered',
            'order_date': 'Order Date',
            'order_reference_document': 'Reference Document',
        }
        widgets = {
            'order_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
    
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter quantity'
        }),
        label='Quantity Ordered'
    )


class MachinePartForm(ServiceFrequencyMixin, SparkCRUDForm):
    """Form for adding parts to machines with service instructions"""
    
    class Meta:
        model = MachinePart
        fields = ['machine', 'part', 'running_hours', 'service_frequency', 'service_frequency_unit', 'service_instructions']
        labels = {
            'machine': 'Machine',
            'part': 'Part',
            'running_hours': 'Running Hours',
            'service_frequency': 'Service Frequency',
            'service_frequency_unit': 'Frequency Unit',
            'service_instructions': 'Service Instructions',
        }
    
    # Override servive frequency for custom styling
    service_frequency = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Frequency number'
        }),
        label='Service Frequency'
    )
    # Override service instructions for custom styling
    service_instructions = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': 'Enter detailed service instructions...'
        }),
        label='Service Instructions'
    )


class MachineSearchForm(SparkSearchForm):
    """Form for searching machines"""
    
    name = forms.CharField(
        label='Machine Name',
        required=False
    )
    
    tag_number = forms.CharField(
        label='Tag Number',
        required=False
    )
    
    location = forms.CharField(
        label='Location',
        required=False
    )


class PartSearchForm(SparkSearchForm):
    """Form for searching parts"""

    part_no = forms.CharField(
        label='Part Number',
        required=False
    )

    stock_status = forms.ChoiceField(
        choices=[
            ('', 'All Parts'),
            ('low', 'Low Stock'),
            ('adequate', 'Adequate Stock'),
            ('overstocked', 'Overstocked'),
        ],
        required=False,
        label='Stock Status',
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class MachineSystemSearchForm(SparkSearchForm):
    """Form for searching machine systems"""

    name = forms.CharField(
        label='System Name',
        required=False
    )
