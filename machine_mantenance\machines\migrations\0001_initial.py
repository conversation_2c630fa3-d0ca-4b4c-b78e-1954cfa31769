# Generated by Django 5.2.1 on 2025-08-25 14:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Machine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('serial_number', models.CharField(blank=True, max_length=100, null=True)),
                ('tag_number', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('location', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Part',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('miniumum_stock', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='MachinePart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_frequency', models.IntegerField()),
                ('service_frequency_unit', models.CharField(choices=[('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], max_length=100)),
                ('service_instructions', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('machine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parts_composed', to='machines.machine')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='machines_comprised_of', to='machines.part')),
            ],
        ),
        migrations.CreateModel(
            name='PartOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField()),
                ('order_date', models.DateField()),
                ('order_reference_document', models.FileField(blank=True, null=True, upload_to='part_orders/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_orders', to='machines.part')),
            ],
        ),
    ]
