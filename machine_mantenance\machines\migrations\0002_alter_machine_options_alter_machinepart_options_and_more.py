# Generated by Django 5.2.1 on 2025-08-25 17:48

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='machine',
            options={'ordering': ['name'], 'verbose_name': 'Machine', 'verbose_name_plural': 'Machines'},
        ),
        migrations.AlterModelOptions(
            name='machinepart',
            options={'ordering': ['machine__name', 'part__name'], 'verbose_name': 'Machine Part', 'verbose_name_plural': 'Machine Parts'},
        ),
        migrations.AlterModelOptions(
            name='part',
            options={'ordering': ['name'], 'verbose_name': 'Part', 'verbose_name_plural': 'Parts'},
        ),
        migrations.AlterModelOptions(
            name='partorder',
            options={'ordering': ['-order_date'], 'verbose_name': 'Part Order', 'verbose_name_plural': 'Part Orders'},
        ),
        migrations.RemoveField(
            model_name='part',
            name='miniumum_stock',
        ),
        migrations.AddField(
            model_name='part',
            name='minimum_stock',
            field=models.IntegerField(default=0, help_text='Minimum quantity that should be kept in stock', validators=[django.core.validators.MinValueValidator(0)], verbose_name='Minimum Stock Level'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='machine',
            name='description',
            field=models.TextField(verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='machine',
            name='location',
            field=models.CharField(max_length=200, verbose_name='Location'),
        ),
        migrations.AlterField(
            model_name='machine',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Machine Name'),
        ),
        migrations.AlterField(
            model_name='machine',
            name='serial_number',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Serial Number'),
        ),
        migrations.AlterField(
            model_name='machine',
            name='tag_number',
            field=models.CharField(max_length=100, unique=True, verbose_name='Tag Number'),
        ),
        migrations.AlterField(
            model_name='machinepart',
            name='machine',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='machine_parts', to='machines.machine', verbose_name='Machine'),
        ),
        migrations.AlterField(
            model_name='machinepart',
            name='part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_machines', to='machines.part', verbose_name='Part'),
        ),
        migrations.AlterField(
            model_name='machinepart',
            name='service_frequency',
            field=models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='Service Frequency'),
        ),
        migrations.AlterField(
            model_name='machinepart',
            name='service_frequency_unit',
            field=models.CharField(choices=[('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], max_length=10, verbose_name='Frequency Unit'),
        ),
        migrations.AlterField(
            model_name='machinepart',
            name='service_instructions',
            field=models.TextField(verbose_name='Service Instructions'),
        ),
        migrations.AlterField(
            model_name='part',
            name='description',
            field=models.TextField(verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='part',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Part Name'),
        ),
        migrations.AlterField(
            model_name='partorder',
            name='order_date',
            field=models.DateField(verbose_name='Order Date'),
        ),
        migrations.AlterField(
            model_name='partorder',
            name='order_reference_document',
            field=models.FileField(blank=True, help_text='Upload purchase order or receipt', null=True, upload_to='part_orders/', verbose_name='Reference Document'),
        ),
        migrations.AlterField(
            model_name='partorder',
            name='part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_orders', to='machines.part', verbose_name='Part'),
        ),
        migrations.AlterField(
            model_name='partorder',
            name='quantity',
            field=models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='Quantity Ordered'),
        ),
        migrations.AlterUniqueTogether(
            name='machinepart',
            unique_together={('machine', 'part')},
        ),
    ]
