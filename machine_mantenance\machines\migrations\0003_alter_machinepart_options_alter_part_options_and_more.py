# Generated by Django 5.2.1 on 2025-08-31 08:34

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0002_alter_machine_options_alter_machinepart_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='machinepart',
            options={'ordering': ['machine__name', 'part__part_no'], 'verbose_name': 'Machine Part', 'verbose_name_plural': 'Machine Parts'},
        ),
        migrations.AlterModelOptions(
            name='part',
            options={'ordering': ['part_no'], 'verbose_name': 'Part', 'verbose_name_plural': 'Parts'},
        ),
        migrations.RemoveField(
            model_name='part',
            name='name',
        ),
        migrations.AddField(
            model_name='machinepart',
            name='running_hours',
            field=models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Running Hours'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='part',
            name='part_no',
            field=models.CharField(default='part', max_length=100, verbose_name='Part Number'),
            preserve_default=False,
        ),
    ]
