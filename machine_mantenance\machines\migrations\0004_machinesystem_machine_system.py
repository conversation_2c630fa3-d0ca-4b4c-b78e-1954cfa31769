# Generated by Django 5.2.1 on 2025-09-04 09:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0003_alter_machinepart_options_alter_part_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MachineSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='System Name')),
            ],
            options={
                'verbose_name': 'Machine System',
                'verbose_name_plural': 'Machine Systems',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='machine',
            name='system',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='machines', to='machines.machinesystem', verbose_name='Machine System'),
            preserve_default=False,
        ),
    ]
