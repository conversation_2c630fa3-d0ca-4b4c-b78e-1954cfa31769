from django.db import models

# Create your models here.
class Machine(models.Model):
    name = models.CharField(max_length=100)
    serial_number = models.CharField(max_length=100, blank=True, null=True)
    tag_number = models.CharField(max_length=100)
    description = models.TextField()
    location = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name
    

class Part(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    miniumum_stock = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name
    
class PartOrder(models.Model):
    part = models.ForeignKey(Part, related_name='part_orders', on_delete=models.CASCADE)
    quantity = models.IntegerField()
    order_date = models.DateField()
    order_reference_document = models.FileField(upload_to='part_orders/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.part.name
    
class MachinePart(models.Model):
    FREQUENCY_UNITS = [
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]
    machine = models.ForeignKey(Machine, related_name='parts_composed', on_delete=models.CASCADE)
    part = models.ForeignKey(Part, related_name='machines_comprised_of', on_delete=models.CASCADE)
    service_frequency = models.IntegerField()
    service_frequency_unit = models.CharField(max_length=100, choices=FREQUENCY_UNITS)
    service_instructions = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.machine.name + ' - ' + self.part.name
    