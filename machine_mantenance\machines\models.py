from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from datetime import timedelta

# Create your models here.
class Machine(models.Model):
    name = models.CharField(max_length=100, verbose_name="Machine Name")
    serial_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="Serial Number")
    tag_number = models.CharField(max_length=100, unique=True, verbose_name="Tag Number")
    description = models.TextField(verbose_name="Description")
    location = models.Char<PERSON>ield(max_length=200, verbose_name="Location")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Machine"
        verbose_name_plural = "Machines"

    def __str__(self):
        return f"{self.name} (Tag #: {self.tag_number})"


class Part(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100, verbose_name="Part Name")
    description = models.TextField(verbose_name="Description")
    minimum_stock = models.IntegerField(
        validators=[MinValueValidator(0)],
        verbose_name="Minimum Stock Level",
        help_text="Minimum quantity that should be kept in stock"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Part"
        verbose_name_plural = "Parts"

    def __str__(self):
        return self.name

    @property
    def current_stock(self):
        """Calculate current stock based on orders minus consumption"""
        total_ordered = sum(order.quantity for order in self.part_orders.all())
        total_consumed = sum(record.number_of_parts_replaced for record in self.get_service_records())
        return total_ordered - total_consumed

    @property
    def stock_status(self):
        """Return stock status: 'low', 'adequate', 'overstocked'"""
        current = self.current_stock
        if current < self.minimum_stock:
            return 'low'
        elif current < self.minimum_stock * 2:
            return 'adequate'
        else:
            return 'overstocked'

    def get_service_records(self):
        """Get all service records for this part across all machines"""
        from service.models import ServiceRecord
        return ServiceRecord.objects.filter(machine_part__part=self)

class PartOrder(models.Model):
    part = models.ForeignKey(Part, related_name='part_orders', on_delete=models.CASCADE, verbose_name="Part")
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Quantity Ordered"
    )
    order_date = models.DateField(verbose_name="Order Date")
    order_reference_document = models.FileField(
        upload_to='part_orders/',
        blank=True,
        null=True,
        verbose_name="Reference Document",
        help_text="Upload purchase order or receipt"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-order_date']
        verbose_name = "Part Order"
        verbose_name_plural = "Part Orders"

    def __str__(self):
        return f"{self.part.name} - {self.quantity} units ({self.order_date})"

class MachinePart(models.Model):
    FREQUENCY_UNITS = [
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]
    machine = models.ForeignKey(Machine, related_name='machine_parts', on_delete=models.CASCADE, verbose_name="Machine")
    part = models.ForeignKey(Part, related_name='part_machines', on_delete=models.CASCADE, verbose_name="Part")
    service_frequency = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Service Frequency"
    )
    service_frequency_unit = models.CharField(
        max_length=10,
        choices=FREQUENCY_UNITS,
        verbose_name="Frequency Unit"
    )
    service_instructions = models.TextField(verbose_name="Service Instructions")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['machine', 'part']
        ordering = ['machine__name', 'part__name']
        verbose_name = "Machine Part"
        verbose_name_plural = "Machine Parts"

    def __str__(self):
        return f"{self.machine.name} - {self.part.name}"

    def get_frequency_in_days(self):
        """Convert service frequency to days for calculations"""
        if self.service_frequency_unit == 'days':
            return self.service_frequency
        elif self.service_frequency_unit == 'weeks':
            return self.service_frequency * 7
        elif self.service_frequency_unit == 'months':
            return self.service_frequency * 30  # Approximate
        return 0

    def get_next_service_date(self):
        """Calculate next service date based on last service record"""
        last_service = self.service_records.order_by('-service_date').first()
        frequency_days = self.get_frequency_in_days()
        if last_service:
            return last_service.service_date + timedelta(days=frequency_days)
        return self.created_at.date() + timedelta(days=frequency_days) # If no service history, set service schedule

    def is_overdue(self):
        """Check if service is overdue"""
        next_service = self.get_next_service_date()
        return timezone.now().date() > next_service
    