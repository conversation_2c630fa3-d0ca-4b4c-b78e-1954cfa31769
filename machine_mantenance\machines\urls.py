"""
URL patterns for machine management.
"""
from django.urls import path
from . import views

app_name = 'machines'

urlpatterns = [
    # Machine URLs
    path('', views.machine_list, name='list'),
    path('<int:pk>/', views.machine_detail, name='detail'),
    path('create/', views.machine_create, name='create'),
    path('<int:pk>/edit/', views.machine_edit, name='edit'),
    path('<int:pk>/delete/', views.machine_delete, name='delete'),
    
    # Machine Part URLs
    path('add-part/', views.machine_part_create, name='add_part'),
    path('<int:machine_pk>/add-part/', views.machine_part_create, name='add_part_to_machine'),
    path('machine-part/<int:pk>/edit/', views.machine_part_edit, name='edit_machine_part'),
]
