from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import Machine, Part, PartOrder, MachinePart
from .forms import (
    MachineForm, PartForm, PartOrderForm, MachinePartForm,
    MachineSearchForm, PartSearchForm
)


# Machine Views
def machine_list(request):
    """List all machines with search functionality"""
    search_form = MachineSearchForm(request.GET)
    machines = Machine.objects.all()

    if search_form.is_valid():
        name = search_form.cleaned_data.get('name')
        tag_number = search_form.cleaned_data.get('tag_number')
        location = search_form.cleaned_data.get('location')

        if name:
            machines = machines.filter(name__icontains=name)
        if tag_number:
            machines = machines.filter(tag_number__icontains=tag_number)
        if location:
            machines = machines.filter(location__icontains=location)

    paginator = Paginator(machines, 25)  # Show 25 machines per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': machines.count(),
    }
    return render(request, 'pages/machines/machine_list.html', context)


def machine_detail(request, pk):
    """Display machine details with associated parts"""
    machine = get_object_or_404(Machine, pk=pk)
    machine_parts = machine.machine_parts.all()

    context = {
        'machine': machine,
        'machine_parts': machine_parts,
    }
    return render(request, 'pages/machines/machine_detail.html', context)


def machine_create(request):
    """Create a new machine"""
    if request.method == 'POST':
        form = MachineForm(request.POST)
        if form.is_valid():
            machine = form.save()
            messages.success(request, f'Machine "{machine.name}" created successfully!')
            return redirect('machines:detail', pk=machine.pk)
    else:
        form = MachineForm()

    context = {
        'form': form,
        'title': 'Add New Machine',
        'submit_text': 'Create Machine',
    }
    return render(request, 'pages/machines/machine_form.html', context)


def machine_edit(request, pk):
    """Edit an existing machine"""
    machine = get_object_or_404(Machine, pk=pk)

    if request.method == 'POST':
        form = MachineForm(request.POST, instance=machine)
        if form.is_valid():
            machine = form.save()
            messages.success(request, f'Machine "{machine.name}" updated successfully!')
            return redirect('machines:detail', pk=machine.pk)
    else:
        form = MachineForm(instance=machine)

    context = {
        'form': form,
        'machine': machine,
        'title': f'Edit Machine: {machine.name}',
        'submit_text': 'Update Machine',
    }
    return render(request, 'pages/machines/machine_form.html', context)


def machine_delete(request, pk):
    """Delete a machine"""
    machine = get_object_or_404(Machine, pk=pk)

    if request.method == 'POST':
        machine_name = machine.name
        machine.delete()
        messages.success(request, f'Machine "{machine_name}" deleted successfully!')
        return redirect('machines:list')

    context = {
        'machine': machine,
    }
    return render(request, 'pages/machines/machine_confirm_delete.html', context)


# Part Views
def part_list(request):
    """List all parts with search functionality and stock status"""
    search_form = PartSearchForm(request.GET)
    parts = Part.objects.all()

    if search_form.is_valid():
        part_no = search_form.cleaned_data.get('part_no')
        stock_status = search_form.cleaned_data.get('stock_status')

        if part_no:
            parts = parts.filter(part_no__icontains=part_no)

        # Filter by stock status if specified
        if stock_status:
            filtered_parts = []
            for part in parts:
                if part.stock_status == stock_status:
                    filtered_parts.append(part.pk)
            parts = parts.filter(pk__in=filtered_parts)

    paginator = Paginator(parts, 25)  # Show 25 parts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': parts.count(),
    }
    return render(request, 'pages/parts/part_list.html', context)


def part_detail(request, pk):
    """Display part details with stock information and usage history"""
    part = get_object_or_404(Part, pk=pk)
    part_orders = part.part_orders.all()[:10]  # Recent orders
    machine_parts = part.part_machines.all()  # Machines using this part

    context = {
        'part': part,
        'part_orders': part_orders,
        'machine_parts': machine_parts,
        'current_stock': part.current_stock,
        'stock_status': part.stock_status,
    }
    return render(request, 'pages/parts/part_detail.html', context)


def part_create(request):
    """Create a new part"""
    if request.method == 'POST':
        form = PartForm(request.POST)
        if form.is_valid():
            part = form.save()
            messages.success(request, f'Part "{part.part_no}" created successfully!')
            return redirect('parts:detail', pk=part.pk)
    else:
        form = PartForm()

    context = {
        'form': form,
        'title': 'Add New Part',
        'submit_text': 'Create Part',
    }
    return render(request, 'pages/parts/part_form.html', context)


def part_edit(request, pk):
    """Edit an existing part"""
    part = get_object_or_404(Part, pk=pk)

    if request.method == 'POST':
        form = PartForm(request.POST, instance=part)
        if form.is_valid():
            part = form.save()
            messages.success(request, f'Part "{part.part_no}" updated successfully!')
            return redirect('parts:detail', pk=part.pk)
    else:
        form = PartForm(instance=part)

    context = {
        'form': form,
        'part': part,
        'title': f'Edit Part: {part.part_no}',
        'submit_text': 'Update Part',
    }
    return render(request, 'pages/parts/part_form.html', context)


# Part Order Views
def part_order_create(request, part_pk=None):
    """Create a new part order"""
    part = None
    if part_pk:
        part = get_object_or_404(Part, pk=part_pk)

    if request.method == 'POST':
        form = PartOrderForm(request.POST, request.FILES)
        if form.is_valid():
            part_order = form.save()
            messages.success(request, f'Part order for "{part_order.part.part_no}" created successfully!')
            return redirect('parts:detail', pk=part_order.part.pk)
    else:
        initial = {'part': part} if part else {}
        form = PartOrderForm(initial=initial)

    context = {
        'form': form,
        'part': part,
        'title': 'Add Part Order',
        'submit_text': 'Create Order',
    }
    return render(request, 'pages/parts/part_order_form.html', context)


# Machine Part Views
def machine_part_create(request, machine_pk=None):
    """Add a part to a machine with service instructions"""
    machine = None
    if machine_pk:
        machine = get_object_or_404(Machine, pk=machine_pk)

    if request.method == 'POST':
        form = MachinePartForm(request.POST)
        if form.is_valid():
            machine_part = form.save()
            messages.success(request, f'Part "{machine_part.part.part_no}" added to machine "{machine_part.machine.name}" successfully!')

            # Inform user about automatic schedule creation
            next_service_date = machine_part.get_next_service_date()
            messages.info(request, f'Initial service automatically scheduled for {next_service_date.strftime("%B %d, %Y")}.')

            # Check if user clicked "Save and Add Another"
            if 'save_and_add_another' in request.POST:
                # Redirect back to the form with the same machine pre-selected
                return redirect('machines:add_part_to_machine', machine_pk=machine_part.machine.pk)
            else:
                # Regular save - redirect to machine detail
                return redirect('machines:detail', pk=machine_part.machine.pk)
    else:
        initial = {'machine': machine} if machine else {}
        form = MachinePartForm(initial=initial)

    context = {
        'form': form,
        'machine': machine,
        'title': 'Add Part to Machine',
        'submit_text': 'Add Part',
    }
    return render(request, 'pages/machines/machine_part_form.html', context)


def machine_part_edit(request, pk):
    """Edit machine part service instructions"""
    machine_part = get_object_or_404(MachinePart, pk=pk)

    if request.method == 'POST':
        # Store original frequency to detect changes
        original_frequency = machine_part.service_frequency
        original_unit = machine_part.service_frequency_unit

        form = MachinePartForm(request.POST, instance=machine_part)
        if form.is_valid():
            machine_part = form.save()
            messages.success(request, f'Machine part "{machine_part}" updated successfully!')

            # Check if service frequency changed
            if (machine_part.service_frequency != original_frequency or
                machine_part.service_frequency_unit != original_unit):

                # Update future service schedules if frequency changed
                from service.models import ServiceSchedule
                future_schedules = ServiceSchedule.objects.filter(
                    machine_part=machine_part,
                    scheduled_date__gt=timezone.now().date()
                )

                if future_schedules.exists():
                    messages.info(request, f'Service frequency updated. Consider reviewing {future_schedules.count()} future service schedule(s).')

            return redirect('machines:detail', pk=machine_part.machine.pk)
    else:
        form = MachinePartForm(instance=machine_part)

    context = {
        'form': form,
        'machine_part': machine_part,
        'title': f'Edit Machine Part: {machine_part}',
        'submit_text': 'Update Part',
    }
    return render(request, 'pages/machines/machine_part_form.html', context)
