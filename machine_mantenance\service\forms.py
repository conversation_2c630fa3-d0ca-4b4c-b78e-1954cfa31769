"""
Forms for service management in the maintenance system.
"""
from django import forms
from .models import Technician, ServiceSchedule, ServiceTicket, ServiceTicketItem, ServiceRecord
from forms.base import SparkCRUDForm, SparkSearchForm
from forms.mixins import TimestampFieldsMixin, ContactFieldsMixin, CommentsFieldsMixin


class TechnicianForm(ContactFieldsMixin, TimestampFieldsMixin, SparkCRUDForm):
    """Form for creating and editing technicians"""
    
    class Meta:
        model = Technician
        fields = ['name', 'email', 'phone', 'specialization']
        labels = {
            'name': 'Full Name',
            'email': 'Email Address',
            'phone': 'Phone Number',
            'specialization': 'Specialization/Skills',
        }
    
    # Override specialization for custom styling
    specialization = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter technician specializations and skills...'
        }),
        label='Specialization/Skills'
    )


class ServiceScheduleForm(SparkCRUDForm):
    """Form for creating service schedules"""
    
    class Meta:
        model = ServiceSchedule
        fields = ['machine_part', 'assigned_technician', 'scheduled_date']
        labels = {
            'machine_part': 'Machine Part',
            'assigned_technician': 'Assigned Technician',
            'scheduled_date': 'Scheduled Date',
        }
        widgets = {
            'scheduled_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }


class ServiceTicketForm(SparkCRUDForm):
    """Form for creating service tickets"""
    
    class Meta:
        model = ServiceTicket
        fields = ['service_date', 'assigned_technician']
        labels = {
            'service_date': 'Service Date',
            'assigned_technician': 'Assigned Technician',
        }
        widgets = {
            'service_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }


class ServiceTicketItemForm(SparkCRUDForm):
    """Form for adding items to service tickets"""
    
    class Meta:
        model = ServiceTicketItem
        fields = ['service_ticket', 'machine_part', 'referenced_schedule']
        labels = {
            'service_ticket': 'Service Ticket',
            'machine_part': 'Machine Part',
            'referenced_schedule': 'Referenced Schedule',
        }


class ServiceRecordForm(CommentsFieldsMixin, SparkCRUDForm):
    """Form for creating service records"""
    
    class Meta:
        model = ServiceRecord
        fields = [
            'ticket_item', 'machine_part', 'service_date', 
            'number_of_parts_replaced', 'technician', 
            'technician_comments', 'supervisor_comments'
        ]
        labels = {
            'ticket_item': 'Service Ticket Item',
            'machine_part': 'Machine Part',
            'service_date': 'Service Date',
            'number_of_parts_replaced': 'Number of Parts Replaced',
            'technician': 'Technician',
            'technician_comments': 'Technician Comments',
            'supervisor_comments': 'Supervisor Comments',
        }
        widgets = {
            'service_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
    
    # Override number of parts replaced for custom styling
    number_of_parts_replaced = forms.IntegerField(
        min_value=0,
        initial=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter number of parts replaced'
        }),
        label='Number of Parts Replaced'
    )


class TechnicianSearchForm(SparkSearchForm):
    """Form for searching technicians"""
    
    name = forms.CharField(
        label='Technician Name',
        required=False
    )
    
    specialization = forms.CharField(
        label='Specialization',
        required=False
    )


class ServiceRecordSearchForm(SparkSearchForm):
    """Form for searching service records"""
    
    machine_name = forms.CharField(
        label='Machine Name',
        required=False
    )
    
    part_name = forms.CharField(
        label='Part Name',
        required=False
    )
    
    technician_name = forms.CharField(
        label='Technician Name',
        required=False
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Date From'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Date To'
    )
