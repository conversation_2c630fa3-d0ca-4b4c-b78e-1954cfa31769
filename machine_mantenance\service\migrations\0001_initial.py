# Generated by Django 5.2.1 on 2025-08-25 14:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('machines', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Technician',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(max_length=20)),
                ('specialization', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='ServiceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('machine_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_schedules', to='machines.machinepart')),
                ('assigned_technician', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_schedules', to='service.technician')),
            ],
        ),
        migrations.CreateModel(
            name='ServiceTicketItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('machine_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticketed_services', to='machines.machinepart')),
                ('referenced_scedule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items_scheduled', to='service.serviceschedule')),
                ('service_ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticket_items', to='service.serviceticket')),
            ],
        ),
        migrations.AddField(
            model_name='serviceticket',
            name='assigned_technician',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_tickets', to='service.technician'),
        ),
        migrations.CreateModel(
            name='ServiceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_date', models.DateField()),
                ('number_of_parts_replaced', models.PositiveIntegerField()),
                ('technician_comments', models.TextField()),
                ('supervisor_comments', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('machine_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_records', to='machines.machinepart')),
                ('ticket_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_items', to='service.serviceticketitem')),
                ('technician', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='technician_records', to='service.technician')),
            ],
        ),
    ]
