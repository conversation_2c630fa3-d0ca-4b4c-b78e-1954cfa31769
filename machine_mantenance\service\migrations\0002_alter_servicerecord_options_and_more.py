# Generated by Django 5.2.1 on 2025-08-25 17:48

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0002_alter_machine_options_alter_machinepart_options_and_more'),
        ('service', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='servicerecord',
            options={'ordering': ['-service_date'], 'verbose_name': 'Service Record', 'verbose_name_plural': 'Service Records'},
        ),
        migrations.AlterModelOptions(
            name='serviceschedule',
            options={'ordering': ['scheduled_date'], 'verbose_name': 'Service Schedule', 'verbose_name_plural': 'Service Schedules'},
        ),
        migrations.AlterModelOptions(
            name='serviceticket',
            options={'ordering': ['-service_date'], 'verbose_name': 'Service Ticket', 'verbose_name_plural': 'Service Tickets'},
        ),
        migrations.AlterModelOptions(
            name='serviceticketitem',
            options={'verbose_name': 'Service Ticket Item', 'verbose_name_plural': 'Service Ticket Items'},
        ),
        migrations.AlterModelOptions(
            name='technician',
            options={'ordering': ['name'], 'verbose_name': 'Technician', 'verbose_name_plural': 'Technicians'},
        ),
        migrations.AlterUniqueTogether(
            name='serviceticketitem',
            unique_together={('service_ticket', 'machine_part')},
        ),
        migrations.AddField(
            model_name='serviceticketitem',
            name='referenced_schedule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items_scheduled', to='service.serviceschedule', verbose_name='Referenced Schedule'),
        ),
        migrations.AddField(
            model_name='technician',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=datetime.datetime(2025, 8, 25, 17, 48, 13, 637879, tzinfo=datetime.timezone.utc)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='technician',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='machine_part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_records', to='machines.machinepart', verbose_name='Machine Part'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='number_of_parts_replaced',
            field=models.PositiveIntegerField(default=0, verbose_name='Number of Parts Replaced'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='service_date',
            field=models.DateField(verbose_name='Service Date'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='supervisor_comments',
            field=models.TextField(blank=True, help_text='Comments from the supervisor after inspection', verbose_name='Supervisor Comments'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='technician',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_records', to='service.technician', verbose_name='Technician'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='technician_comments',
            field=models.TextField(blank=True, help_text='Comments from the technician who performed the service', verbose_name='Technician Comments'),
        ),
        migrations.AlterField(
            model_name='servicerecord',
            name='ticket_item',
            field=models.ForeignKey(blank=True, help_text='Link to service ticket item if this was a scheduled service', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_records', to='service.serviceticketitem', verbose_name='Service Ticket Item'),
        ),
        migrations.AlterField(
            model_name='serviceschedule',
            name='assigned_technician',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_schedules', to='service.technician', verbose_name='Assigned Technician'),
        ),
        migrations.AlterField(
            model_name='serviceschedule',
            name='machine_part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_schedules', to='machines.machinepart', verbose_name='Machine Part'),
        ),
        migrations.AlterField(
            model_name='serviceschedule',
            name='scheduled_date',
            field=models.DateField(verbose_name='Scheduled Date'),
        ),
        migrations.AlterField(
            model_name='serviceticket',
            name='assigned_technician',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='service_tickets', to='service.technician', verbose_name='Assigned Technician'),
        ),
        migrations.AlterField(
            model_name='serviceticket',
            name='service_date',
            field=models.DateField(verbose_name='Service Date'),
        ),
        migrations.AlterField(
            model_name='serviceticketitem',
            name='machine_part',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticketed_services', to='machines.machinepart', verbose_name='Machine Part'),
        ),
        migrations.AlterField(
            model_name='serviceticketitem',
            name='service_ticket',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticket_items', to='service.serviceticket', verbose_name='Service Ticket'),
        ),
        migrations.AlterField(
            model_name='technician',
            name='email',
            field=models.EmailField(max_length=254, verbose_name='Email Address'),
        ),
        migrations.AlterField(
            model_name='technician',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Full Name'),
        ),
        migrations.AlterField(
            model_name='technician',
            name='phone',
            field=models.CharField(max_length=20, verbose_name='Phone Number'),
        ),
        migrations.AlterField(
            model_name='technician',
            name='specialization',
            field=models.CharField(max_length=200, verbose_name='Specialization/Skills'),
        ),
        migrations.RemoveField(
            model_name='serviceticketitem',
            name='referenced_scedule',
        ),
    ]
