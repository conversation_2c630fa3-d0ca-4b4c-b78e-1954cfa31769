from django.db import models

class Technician(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    specialization = models.CharField(max_length=200)

    def __str__(self):
        return self.name
    
class ServiceSchedule(models.Model):
    machine_part = models.ForeignKey('machines.MachinePart', related_name='service_schedules', on_delete=models.CASCADE)
    assigned_technician = models.ForeignKey(Technician, on_delete=models.CASCADE, related_name='service_schedules', null=True, blank=True)
    scheduled_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Service Schedule for {self.machine_part} on {self.scheduled_date}"
    
class ServiceTicket(models.Model):
    service_date = models.DateField()
    assigned_technician = models.Foreign<PERSON><PERSON>(Technician, on_delete=models.CASCADE, related_name='service_tickets', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Service Ticket for {self.service_date}"
    
class ServiceTicketItem(models.Model):
    service_ticket = models.ForeignKey(ServiceTicket, on_delete=models.CASCADE, related_name='ticket_items')
    machine_part = models.ForeignKey('machines.MachinePart', on_delete=models.CASCADE, related_name='ticketed_services')
    referenced_scedule = models.ForeignKey(ServiceSchedule, on_delete=models.CASCADE, related_name='items_scheduled', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Service Ticket Item for {self.machine_part}"
    
class ServiceRecord(models.Model):
    ticket_item = models.ForeignKey(ServiceTicketItem, on_delete=models.CASCADE, related_name='service_items', blank=True, null=True)
    machine_part = models.ForeignKey('machines.MachinePart', on_delete=models.CASCADE, related_name='service_records')
    service_date = models.DateField()
    number_of_parts_replaced = models.PositiveIntegerField()
    technician = models.ForeignKey(Technician, on_delete=models.CASCADE, related_name='technician_records')
    technician_comments = models.TextField()
    supervisor_comments = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Service Record for {self.machine_part} on {self.service_date}"
