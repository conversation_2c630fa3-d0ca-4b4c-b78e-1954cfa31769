"""
URL patterns for service management.
"""
from django.urls import path
from . import views

app_name = 'service'

urlpatterns = [
    # Service Schedule URLs
    path('schedule/', views.service_schedule_list, name='schedule_list'),
    path('schedule/create/', views.service_schedule_create, name='schedule_create'),
    
    # Service Ticket URLs
    path('tickets/', views.service_ticket_list, name='ticket_list'),
    path('tickets/<int:pk>/', views.service_ticket_detail, name='ticket_detail'),
    path('tickets/create/', views.service_ticket_create, name='ticket_create'),
    
    # Service Record URLs
    path('records/', views.service_record_list, name='record_list'),
    path('records/<int:pk>/', views.service_record_detail, name='record_detail'),
    path('records/create/', views.service_record_create, name='record_create'),
    path('records/<int:pk>/edit/', views.service_record_edit, name='record_edit'),
]
