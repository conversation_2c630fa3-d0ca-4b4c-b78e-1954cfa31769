from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import Technician, ServiceSchedule, ServiceTicket, ServiceTicketItem, ServiceRecord
from .forms import (
    TechnicianForm, ServiceScheduleForm, ServiceTicketForm,
    ServiceTicketItemForm, ServiceRecordForm,
    TechnicianSearchForm, ServiceRecordSearchForm
)


# Technician Views
def technician_list(request):
    """List all technicians with search functionality"""
    search_form = TechnicianSearchForm(request.GET)
    technicians = Technician.objects.all()

    if search_form.is_valid():
        name = search_form.cleaned_data.get('name')
        specialization = search_form.cleaned_data.get('specialization')

        if name:
            technicians = technicians.filter(name__icontains=name)
        if specialization:
            technicians = technicians.filter(specialization__icontains=specialization)

    paginator = Paginator(technicians, 25)  # Show 25 technicians per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': technicians.count(),
    }
    return render(request, 'pages/technicians/technician_list.html', context)


def technician_detail(request, pk):
    """Display technician details with job history"""
    technician = get_object_or_404(Technician, pk=pk)
    recent_records = technician.service_records.all()[:10]  # Recent service records
    upcoming_schedules = technician.service_schedules.filter(
        scheduled_date__gte=timezone.now().date()
    )[:10]

    context = {
        'technician': technician,
        'recent_records': recent_records,
        'upcoming_schedules': upcoming_schedules,
    }
    return render(request, 'pages/technicians/technician_detail.html', context)


def technician_create(request):
    """Create a new technician"""
    if request.method == 'POST':
        form = TechnicianForm(request.POST)
        if form.is_valid():
            technician = form.save()
            messages.success(request, f'Technician "{technician.name}" created successfully!')
            return redirect('technicians:detail', pk=technician.pk)
    else:
        form = TechnicianForm()

    context = {
        'form': form,
        'title': 'Add New Technician',
        'submit_text': 'Create Technician',
    }
    return render(request, 'pages/technicians/technician_form.html', context)


def technician_edit(request, pk):
    """Edit an existing technician"""
    technician = get_object_or_404(Technician, pk=pk)

    if request.method == 'POST':
        form = TechnicianForm(request.POST, instance=technician)
        if form.is_valid():
            technician = form.save()
            messages.success(request, f'Technician "{technician.name}" updated successfully!')
            return redirect('technicians:detail', pk=technician.pk)
    else:
        form = TechnicianForm(instance=technician)

    context = {
        'form': form,
        'technician': technician,
        'title': f'Edit Technician: {technician.name}',
        'submit_text': 'Update Technician',
    }
    return render(request, 'pages/technicians/technician_form.html', context)


# Service Schedule Views
def service_schedule_list(request):
    """List all service schedules with status indicators"""
    schedules = ServiceSchedule.objects.all().order_by('scheduled_date')

    # Separate by status
    overdue = []
    due_today = []
    upcoming = []

    for schedule in schedules:
        if schedule.status == 'overdue':
            overdue.append(schedule)
        elif schedule.status == 'due_today':
            due_today.append(schedule)
        else:
            upcoming.append(schedule)

    context = {
        'overdue': overdue,
        'due_today': due_today,
        'upcoming': upcoming[:20],  # Limit upcoming to 20 items
    }
    return render(request, 'pages/service/schedule_list.html', context)


def service_schedule_create(request):
    """Create a new service schedule"""
    if request.method == 'POST':
        form = ServiceScheduleForm(request.POST)
        if form.is_valid():
            schedule = form.save()
            messages.success(request, f'Service scheduled for {schedule.machine_part} on {schedule.scheduled_date}!')
            return redirect('service:schedule_list')
    else:
        form = ServiceScheduleForm()

    context = {
        'form': form,
        'title': 'Schedule Service',
        'submit_text': 'Create Schedule',
    }
    return render(request, 'pages/service/schedule_form.html', context)


# Service Ticket Views
def service_ticket_list(request):
    """List all service tickets"""
    tickets = ServiceTicket.objects.all()

    paginator = Paginator(tickets, 25)  # Show 25 tickets per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': tickets.count(),
    }
    return render(request, 'pages/service/ticket_list.html', context)


def service_ticket_detail(request, pk):
    """Display service ticket details with items"""
    ticket = get_object_or_404(ServiceTicket, pk=pk)
    ticket_items = ticket.ticket_items.all()

    context = {
        'ticket': ticket,
        'ticket_items': ticket_items,
    }
    return render(request, 'pages/service/ticket_detail.html', context)


def service_ticket_create(request):
    """Create a new service ticket"""
    if request.method == 'POST':
        form = ServiceTicketForm(request.POST)
        if form.is_valid():
            ticket = form.save()
            messages.success(request, f'Service ticket created for {ticket.service_date}!')
            return redirect('service:ticket_detail', pk=ticket.pk)
    else:
        form = ServiceTicketForm()

    context = {
        'form': form,
        'title': 'Create Service Ticket',
        'submit_text': 'Create Ticket',
    }
    return render(request, 'pages/service/ticket_form.html', context)


# Service Record Views
def service_record_list(request):
    """List all service records with search functionality"""
    search_form = ServiceRecordSearchForm(request.GET)
    records = ServiceRecord.objects.all()

    if search_form.is_valid():
        machine_name = search_form.cleaned_data.get('machine_name')
        part_name = search_form.cleaned_data.get('part_name')
        technician_name = search_form.cleaned_data.get('technician_name')
        date_from = search_form.cleaned_data.get('date_from')
        date_to = search_form.cleaned_data.get('date_to')

        if machine_name:
            records = records.filter(machine_part__machine__name__icontains=machine_name)
        if part_name:
            records = records.filter(machine_part__part__name__icontains=part_name)
        if technician_name:
            records = records.filter(technician__name__icontains=technician_name)
        if date_from:
            records = records.filter(service_date__gte=date_from)
        if date_to:
            records = records.filter(service_date__lte=date_to)

    paginator = Paginator(records, 25)  # Show 25 records per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': records.count(),
    }
    return render(request, 'pages/service/record_list.html', context)


def service_record_detail(request, pk):
    """Display service record details"""
    record = get_object_or_404(ServiceRecord, pk=pk)

    context = {
        'record': record,
    }
    return render(request, 'pages/service/record_detail.html', context)


def service_record_create(request):
    """Create a new service record"""
    if request.method == 'POST':
        form = ServiceRecordForm(request.POST)
        if form.is_valid():
            record = form.save()
            messages.success(request, f'Service record created for {record.machine_part}!')
            return redirect('service:record_detail', pk=record.pk)
    else:
        form = ServiceRecordForm()

    context = {
        'form': form,
        'title': 'Create Service Record',
        'submit_text': 'Create Record',
    }
    return render(request, 'pages/service/record_form.html', context)


def service_record_edit(request, pk):
    """Edit an existing service record"""
    record = get_object_or_404(ServiceRecord, pk=pk)

    if request.method == 'POST':
        form = ServiceRecordForm(request.POST, instance=record)
        if form.is_valid():
            record = form.save()
            messages.success(request, f'Service record updated successfully!')
            return redirect('service:record_detail', pk=record.pk)
    else:
        form = ServiceRecordForm(instance=record)

    context = {
        'form': form,
        'record': record,
        'title': f'Edit Service Record',
        'submit_text': 'Update Record',
    }
    return render(request, 'pages/service/record_form.html', context)
