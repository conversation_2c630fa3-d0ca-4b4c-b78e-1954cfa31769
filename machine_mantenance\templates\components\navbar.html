{% load static %}
<nav class="navbar navbar-expand navbar-theme">
    <a class="sidebar-toggle d-flex me-2">
        <i class="hamburger align-self-center"></i>
    </a>

    <form class="d-none d-sm-inline-block" method="get" action="#">
        <input class="form-control form-control-lite" type="text" name="q" placeholder="Search projects..." value="{{ request.GET.q }}">
    </form>

    <div class="navbar-collapse collapse">
        <ul class="navbar-nav ms-auto">
            <!-- Messages Dropdown -->
            <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle position-relative" href="#" id="messagesDropdown" data-bs-toggle="dropdown">
                    <i class="align-middle fas fa-envelope-open"></i>
                    {% if unread_messages_count %}
                        <span class="indicator"></span>
                    {% endif %}
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0" aria-labelledby="messagesDropdown">
                    <div class="dropdown-menu-header">
                        <div class="position-relative">
                            {{ unread_messages_count|default:0 }} New Messages
                        </div>
                    </div>
                    <div class="list-group">
                        {% for message in recent_messages %}
                        <a href="#ssage.id %}" class="list-group-item">
                            <div class="row g-0 align-items-center">
                                <div class="col-2">
                                    <img src="{% if message.sender.profile.avatar %}{{ message.sender.profile.avatar.url }}{% else %}{% static 'img/avatars/avatar.jpg' %}{% endif %}" 
                                         class="avatar img-fluid rounded-circle" alt="{{ message.sender.get_full_name }}">
                                </div>
                                <div class="col-10 ps-2">
                                    <div class="text-dark">{{ message.sender.get_full_name }}</div>
                                    <div class="text-muted small mt-1">{{ message.content|truncatechars:50 }}</div>
                                    <div class="text-muted small mt-1">{{ message.created_at|timesince }} ago</div>
                                </div>
                            </div>
                        </a>
                        {% empty %}
                        <div class="list-group-item text-center text-muted">
                            No messages
                        </div>
                        {% endfor %}
                    </div>
                    <div class="dropdown-menu-footer">
                        <a href="#" class="text-muted">Show all messages</a>
                    </div>
                </div>
            </li>

            <!-- Notifications Dropdown -->
            <li class="nav-item dropdown ms-lg-2">
                <a class="nav-link dropdown-toggle position-relative" href="#" id="alertsDropdown" data-bs-toggle="dropdown">
                    <i class="align-middle fas fa-bell"></i>
                    {% if unread_notifications_count %}
                        <span class="indicator"></span>
                    {% endif %}
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0" aria-labelledby="alertsDropdown">
                    <div class="dropdown-menu-header">
                        {{ unread_notifications_count|default:0 }} New Notifications
                    </div>
                    <div class="list-group">
                        {% for notification in recent_notifications %}
                        <a href="#tification.id %}" class="list-group-item">
                            <div class="row g-0 align-items-center">
                                <div class="col-2">
                                    <div class="text-center">
                                        <i class="align-middle {{ notification.icon_class|default:'fas fa-info-circle' }} text-{{ notification.type|default:'primary' }}"></i>
                                    </div>
                                </div>
                                <div class="col-10 ps-2">
                                    <div class="text-dark">{{ notification.title }}</div>
                                    <div class="text-muted small mt-1">{{ notification.message|truncatechars:50 }}</div>
                                    <div class="text-muted small mt-1">{{ notification.created_at|timesince }} ago</div>
                                </div>
                            </div>
                        </a>
                        {% empty %}
                        <div class="list-group-item text-center text-muted">
                            No notifications
                        </div>
                        {% endfor %}
                    </div>
                    <div class="dropdown-menu-footer">
                        <a href="#" class="text-muted">Show all notifications</a>
                    </div>
                </div>
            </li>

            <!-- User Settings Dropdown -->
            <li class="nav-item dropdown ms-lg-2">
                <a class="nav-link dropdown-toggle position-relative" href="#" id="userDropdown" data-bs-toggle="dropdown">
                    <i class="align-middle fas fa-cog"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <a class="dropdown-item" href="#">
                        <i class="align-middle me-1 fas fa-fw fa-user"></i> View Profile
                    </a>
                    <a class="dropdown-item" href="#">
                        <i class="align-middle me-1 fas fa-fw fa-comments"></i> Contacts
                    </a>
                    <a class="dropdown-item" href="#">
                        <i class="align-middle me-1 fas fa-fw fa-chart-pie"></i> Analytics
                    </a>
                    <a class="dropdown-item" href="#">
                        <i class="align-middle me-1 fas fa-fw fa-cogs"></i> Settings
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#">
                        <i class="align-middle me-1 fas fa-fw fa-arrow-alt-circle-right"></i> Sign out
                    </a>
                </div>
            </li>
        </ul>
    </div>
</nav>
