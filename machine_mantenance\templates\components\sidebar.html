{% load static %}
<nav id="sidebar" class="sidebar">
    <a class='sidebar-brand' href='#'>
        <svg>
            <use xlink:href="#ion-ios-pulse-strong"></use>
        </svg>
        Spark
    </a>
    <div class="sidebar-content">
        <div class="sidebar-user">
            <img src="{% static 'img/avatars/avatar.jpg' %}" class="img-fluid rounded-circle mb-2" alt="{{ user.get_full_name|default:user.username }}" />
            <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
            <small>{{ user.profile.title|default:"User" }}</small>
        </div>

        <ul class="sidebar-nav">
            <li class="sidebar-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "dashboard:home" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-home"></i> <span class="align-middle">Dashboard</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'machines' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "machines:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-industry"></i> <span class="align-middle">Machines</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'parts' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "parts:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-cubes"></i> <span class="align-middle">Parts</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'service' and 'schedule' in request.resolver_match.url_name %}active{% endif %}">
                <a class='sidebar-link' href='{% url "service:schedule_list" %}'>
                    <i class="align-middle me-2 far fa-fw fa-calendar-alt"></i> <span class="align-middle">Schedule</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'service' and 'record' in request.resolver_match.url_name %}active{% endif %}">
                <a class='sidebar-link' href='{% url "service:record_list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-wrench"></i> <span class="align-middle">Service Records</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'technicians' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "technicians:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-user-tag"></i> <span class="align-middle">Technicians</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'docs' %}active{% endif %}">
                <a data-bs-target="#documentation" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'docs' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-book"></i> <span class="align-middle">Documentation</span>
                </a>
                <ul id="documentation" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'docs' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'getting_started' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Getting Started</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'plugins' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Plugins</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'changelog' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Changelog</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
