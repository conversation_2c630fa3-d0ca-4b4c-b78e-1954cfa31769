{% comment %}
Reusable card widget component

Usage:
{% include 'components/widgets/card.html' with title="Card Title" content="Card content" %}

Parameters:
- title: Card title (optional)
- content: Card body content (optional)
- card_class: Additional CSS classes for the card (optional)
- header_actions: HTML for header actions (optional)
- footer: Card footer content (optional)
{% endcomment %}

<div class="card {% if card_class %}{{ card_class }}{% endif %}">
    {% if title or header_actions %}
    <div class="card-header">
        {% if header_actions %}
        <div class="card-actions float-end">
            {{ header_actions|safe }}
        </div>
        {% endif %}
        {% if title %}
        <h5 class="card-title mb-0">{{ title }}</h5>
        {% endif %}
    </div>
    {% endif %}
    
    {% if content %}
    <div class="card-body">
        {{ content|safe }}
    </div>
    {% endif %}
    
    {% if footer %}
    <div class="card-footer">
        {{ footer|safe }}
    </div>
    {% endif %}
</div>
