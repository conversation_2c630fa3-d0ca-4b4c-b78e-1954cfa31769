{% comment %}
Chart widget component

Usage:
{% include 'components/widgets/chart.html' with title="Recent Movement" chart_id="chartjs-dashboard-line" chart_type="line" %}

Parameters:
- title: Chart title
- chart_id: Unique ID for the chart canvas
- chart_type: Type of chart (line, bar, pie, etc.)
- chart_class: Additional CSS classes for chart container
- header_actions: HTML for header actions (optional)
- height: Chart height (optional, default: auto)
{% endcomment %}

<div class="card flex-fill w-100">
    <div class="card-header">
        {% if header_actions %}
        <div class="card-actions float-end">
            {{ header_actions|safe }}
        </div>
        {% endif %}
        <h5 class="card-title mb-0">{{ title }}</h5>
    </div>
    <div class="card-body py-3">
        <div class="chart {% if chart_class %}{{ chart_class }}{% endif %}{% if not height %} chart-sm{% endif %}"{% if height %} style="height: {{ height }};"{% endif %}>
            <canvas id="{{ chart_id }}"></canvas>
        </div>
    </div>
</div>
