{% extends 'base/auth_base.html' %}
{% load static %}

{% block title %}Reset Password - {{ block.super }}{% endblock %}

{% block content %}
<div class="text-center mt-4">
    <h1 class="h2">Reset password</h1>
    <p class="lead">
        Enter your email to reset your password.
    </p>
</div>

<div class="card">
    <div class="card-body">
        <div class="m-sm-4">
            <div class="text-center">
                <svg class="avatar img-fluid rounded me-1" width="48" height="48">
                    <use xlink:href="#ion-ios-pulse-strong"></use>
                </svg>
            </div>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="form-label" for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.email.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-text text-muted">
                        We'll send you an email with instructions to reset your password.
                    </small>
                </div>
                <div class="text-center mt-3">
                    <button type="submit" class="btn btn-lg btn-primary">Reset password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="text-center mb-3">
    <a href="{% url 'auth:login' %}" class="text-muted">Back to sign in</a>
</div>
{% endblock %}
