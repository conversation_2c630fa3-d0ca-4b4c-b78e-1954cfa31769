{% extends 'base/base.html' %}
{% load static %}

{% block title %}Form Validation - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Validation
    </h1>
    <p class="header-subtitle">Provide valuable, actionable feedback to your users with HTML5 form validation.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Server-side validation</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">First name</label>
                        <input type="text" class="form-control {% if form.first_name.errors %}is-invalid{% elif form.first_name.value %}is-valid{% endif %}" 
                               name="first_name" value="{{ form.first_name.value|default:'' }}" required>
                        {% if form.first_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.first_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% elif form.first_name.value %}
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Last name</label>
                        <input type="text" class="form-control {% if form.last_name.errors %}is-invalid{% elif form.last_name.value %}is-valid{% endif %}" 
                               name="last_name" value="{{ form.last_name.value|default:'' }}" required>
                        {% if form.last_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.last_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% elif form.last_name.value %}
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="email" class="form-control {% if form.email.errors %}is-invalid{% elif form.email.value %}is-valid{% endif %}" 
                                   name="email" value="{{ form.email.value|default:'' }}" required>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% elif form.email.value %}
                                <div class="valid-feedback">
                                    Looks good!
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">City</label>
                        <input type="text" class="form-control {% if form.city.errors %}is-invalid{% elif form.city.value %}is-valid{% endif %}" 
                               name="city" value="{{ form.city.value|default:'' }}" required>
                        {% if form.city.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.city.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% elif form.city.value %}
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">State</label>
                        <select class="form-select {% if form.state.errors %}is-invalid{% elif form.state.value %}is-valid{% endif %}" 
                                name="state" required>
                            <option value="">Choose...</option>
                            <option value="CA" {% if form.state.value == 'CA' %}selected{% endif %}>California</option>
                            <option value="NY" {% if form.state.value == 'NY' %}selected{% endif %}>New York</option>
                            <option value="TX" {% if form.state.value == 'TX' %}selected{% endif %}>Texas</option>
                        </select>
                        {% if form.state.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.state.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% elif form.state.value %}
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Zip</label>
                        <input type="text" class="form-control {% if form.zip_code.errors %}is-invalid{% elif form.zip_code.value %}is-valid{% endif %}" 
                               name="zip_code" value="{{ form.zip_code.value|default:'' }}" required>
                        {% if form.zip_code.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.zip_code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% elif form.zip_code.value %}
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input {% if form.terms.errors %}is-invalid{% endif %}" 
                                   type="checkbox" name="terms" id="terms" required>
                            <label class="form-check-label" for="terms">
                                Agree to terms and conditions
                            </label>
                            {% if form.terms.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.terms.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <button class="btn btn-primary" type="submit">Submit form</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Client-side validation</h5>
            </div>
            <div class="card-body">
                <form class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label class="form-label">First name</label>
                        <input type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid first name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Last name</label>
                        <input type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid last name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="email" class="form-control" required>
                            <div class="valid-feedback">
                                Looks good!
                            </div>
                            <div class="invalid-feedback">
                                Please choose a valid email.
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">City</label>
                        <input type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid city.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">State</label>
                        <select class="form-select" required>
                            <option selected disabled value="">Choose...</option>
                            <option>California</option>
                            <option>New York</option>
                            <option>Texas</option>
                        </select>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please select a valid state.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Zip</label>
                        <input type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid zip.
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="invalidCheck" required>
                            <label class="form-check-label" for="invalidCheck">
                                Agree to terms and conditions
                            </label>
                            <div class="invalid-feedback">
                                You must agree before submitting.
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" type="submit">Submit form</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Client-side validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>
{% endblock %}
