{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ system.name }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        {{ system.name }}
    </h1>
    <p class="header-subtitle">Machine system details and associated machines.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- System Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">System Information</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'systems:edit' system.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Name:</dt>
                            <dd class="col-sm-8">{{ system.name }}</dd>
                            
                            <dt class="col-sm-4">Machines:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-primary">{{ machines.count }}</span>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ system.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-4">Last Updated:</dt>
                            <dd class="col-sm-8">{{ system.updated_at|date:"M d, Y" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if system.description %}
                <div class="mt-3">
                    <h6>Description:</h6>
                    <p class="text-muted">{{ system.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ machines.count }}</h4>
                            <small class="text-muted">Machines</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-success">0</h4>
                            <small class="text-muted">Active Services</small>
                        </div>
                    </div>
                </div>
                <div class="d-grid">
                    <a href="{% url 'machines:create' %}?system={{ system.pk }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i> Add Machine
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Machines -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">System Machines ({{ machines.count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'machines:create' %}?system={{ system.pk }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Machine
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if machines %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Machine Name</th>
                                <th>Tag Number</th>
                                <th>Location</th>
                                <th>Parts Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for machine in machines %}
                            <tr>
                                <td>
                                    <strong>{{ machine.name }}</strong>
                                    {% if machine.serial_number %}
                                        <br><small class="text-muted">S/N: {{ machine.serial_number }}</small>
                                    {% endif %}
                                </td>
                                <td><span class="badge bg-secondary">{{ machine.tag_number }}</span></td>
                                <td>{{ machine.location }}</td>
                                <td>{{ machine.machine_parts.count }}</td>
                                <td>
                                    <a href="{% url 'machines:detail' machine.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'machines:edit' machine.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-industry fa-3x mb-3"></i>
                    <p>No machines in this system yet.</p>
                    <a href="{% url 'machines:create' %}?system={{ system.pk }}" class="btn btn-primary">Add First Machine</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
