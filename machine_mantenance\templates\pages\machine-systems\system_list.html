{% extends 'base/base.html' %}
{% load static %}

{% block title %}Machine Systems - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Machine Systems
    </h1>
    <p class="header-subtitle">Organize and manage machine categories and groupings.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Systems ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'systems:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add System
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            {{ search_form.name }}
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'systems:list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Systems Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>System Name</th>
                                <th>Description</th>
                                <th>Machine Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for system in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ system.name }}</strong>
                                </td>
                                <td>
                                    {% if system.description %}
                                        {{ system.description|truncatechars:80 }}
                                    {% else %}
                                        <span class="text-muted">No description</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ system.machines.count }}</span>
                                </td>
                                <td>
                                    <a href="{% url 'systems:detail' system.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'systems:edit' system.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fas fa-sitemap fa-3x mb-3"></i>
                                    <p>No machine systems found.</p>
                                    <a href="{% url 'systems:create' %}" class="btn btn-primary">Add First System</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Systems pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
