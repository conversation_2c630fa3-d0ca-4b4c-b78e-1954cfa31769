{% extends 'base/base.html' %}
{% load static %}

{% block title %}Delete {{ machine.name }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title text-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Delete Machine
    </h1>
    <p class="header-subtitle">This action cannot be undone.</p>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-12 col-lg-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirm Deletion
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">Warning!</h6>
                    <p class="mb-0">
                        You are about to permanently delete the machine <strong>"{{ machine.name }}"</strong>. 
                        This will also delete all associated parts, service records, and schedules.
                    </p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Machine Details:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Name:</strong> {{ machine.name }}</li>
                            <li><strong>Tag Number:</strong> {{ machine.tag_number }}</li>
                            {% if machine.serial_number %}
                            <li><strong>Serial Number:</strong> {{ machine.serial_number }}</li>
                            {% endif %}
                            <li><strong>Location:</strong> {{ machine.location }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Impact:</h6>
                        <ul class="list-unstyled text-danger">
                            <li><i class="fas fa-times me-2"></i>{{ machine.machine_parts.count }} machine parts will be deleted</li>
                            <li><i class="fas fa-times me-2"></i>All service schedules will be deleted</li>
                            <li><i class="fas fa-times me-2"></i>All service records will be deleted</li>
                        </ul>
                    </div>
                </div>
                
                <hr>
                
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'machines:detail' machine.pk %}" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Yes, Delete Machine
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
