{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ machine.name }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        {{ machine.name }}
        <span class="badge bg-secondary ms-2">{{ machine.tag_number }}</span>
    </h1>
    <p class="header-subtitle">Machine details and maintenance information.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Machine Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Machine Information</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'machines:edit' machine.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <a href="{% url 'machines:delete' machine.pk %}" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash me-1"></i> Delete
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Name:</dt>
                            <dd class="col-sm-8">{{ machine.name }}</dd>
                            
                            <dt class="col-sm-4">Tag Number:</dt>
                            <dd class="col-sm-8"><span class="badge bg-secondary">{{ machine.tag_number }}</span></dd>
                            
                            {% if machine.serial_number %}
                            <dt class="col-sm-4">Serial Number:</dt>
                            <dd class="col-sm-8">{{ machine.serial_number }}</dd>
                            {% endif %}
                            
                            <dt class="col-sm-4">Location:</dt>
                            <dd class="col-sm-8">{{ machine.location }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ machine.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-4">Last Updated:</dt>
                            <dd class="col-sm-8">{{ machine.updated_at|date:"M d, Y" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if machine.description %}
                <div class="mt-3">
                    <h6>Description:</h6>
                    <p class="text-muted">{{ machine.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ machine_parts.count }}</h4>
                            <small class="text-muted">Parts</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-success">0</h4>
                            <small class="text-muted">Services</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Machine Parts -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Machine Parts ({{ machine_parts.count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'machines:add_part_to_machine' machine.pk %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Part
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if machine_parts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Part Name</th>
                                <th>Service Frequency</th>
                                <th>Next Service</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for machine_part in machine_parts %}
                            <tr>
                                <td>
                                    <strong>{{ machine_part.part.part_no }}</strong>
                                    {% if machine_part.part.description %}
                                        <br><small class="text-muted">{{ machine_part.part.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    Every {{ machine_part.service_frequency }} {{ machine_part.get_service_frequency_unit_display|lower }}
                                </td>
                                <td>{{ machine_part.get_next_service_date|date:"M d, Y" }}</td>
                                <td>
                                    {% if machine_part.is_overdue %}
                                        <span class="badge bg-danger">Overdue</span>
                                    {% else %}
                                        <span class="badge bg-success">Current</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'machines:edit_machine_part' machine_part.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-cubes fa-3x mb-3"></i>
                    <p>No parts added to this machine yet.</p>
                    <a href="{% url 'machines:add_part_to_machine' machine.pk %}" class="btn btn-primary">Add First Part</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
