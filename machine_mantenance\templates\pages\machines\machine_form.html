{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if machine %}
            Update machine information and settings.
        {% else %}
            Add a new machine to the maintenance system.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Machine Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.tag_number.label }}</label>
                                {{ form.tag_number }}
                                {% if form.tag_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.tag_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.serial_number.label }}</label>
                                {{ form.serial_number }}
                                {% if form.serial_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.serial_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.location.label }}</label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.location.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if machine and form.created_at %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.created_at.label }}</label>
                                {{ form.created_at }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.updated_at.label }}</label>
                                {{ form.updated_at }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if machine %}{% url 'machines:detail' machine.pk %}{% else %}{% url 'machines:list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
