{% extends 'base/base.html' %}
{% load static %}

{% block title %}Machines - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Machines
    </h1>
    <p class="header-subtitle">Manage and monitor all shop machinery.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Machines ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'machines:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Machine
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            {{ search_form.name }}
                        </div>
                        <div class="col-md-4">
                            {{ search_form.tag_number }}
                        </div>
                        <div class="col-md-4">
                            {{ search_form.location }}
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'machines:list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Machines Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>System</th>
                                <th>Tag Number</th>
                                <th>Location</th>
                                <th>Parts Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for machine in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ machine.name }}</strong>
                                    {% if machine.serial_number %}
                                        <br><small class="text-muted">S/N: {{ machine.serial_number }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if machine.system %}
                                        <a href="{% url 'systems:detail' machine.system.pk %}" class="text-decoration-none">
                                            <small class="text-primary">{{ machine.system.name }}</small>
                                        </a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td><span class="badge bg-secondary">{{ machine.tag_number }}</span></td>
                                <td>{{ machine.location }}</td>
                                <td>{{ machine.machine_parts.count }}</td>
                                <td>
                                    <a href="{% url 'machines:detail' machine.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'machines:edit' machine.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="fas fa-industry fa-3x mb-3"></i>
                                    <p>No machines found.</p>
                                    <a href="{% url 'machines:create' %}" class="btn btn-primary">Add First Machine</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Machines pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.tag_number %}&tag_number={{ request.GET.tag_number }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.tag_number %}&tag_number={{ request.GET.tag_number }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.tag_number %}&tag_number={{ request.GET.tag_number }}{% endif %}{% if request.GET.location %}&location={{ request.GET.location }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
