{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if machine_part %}
            Update part service instructions and frequency.
        {% elif machine %}
            Add a part to {{ machine.name }} with service instructions.
        {% else %}
            Add a part to a machine with service instructions.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Part Configuration</h5>
            </div>
            <div class="card-body">
                <form method="post" id="machine-part-form">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.machine.label }}</label>
                                {{ form.machine }}
                                {% if form.machine.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.machine.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.part.label }}</label>
                                {{ form.part }}
                                {% if form.part.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.part.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.service_frequency.label }}</label>
                                {{ form.service_frequency }}
                                {% if form.service_frequency.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_frequency.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.service_frequency_unit.label }}</label>
                                {{ form.service_frequency_unit }}
                                {% if form.service_frequency_unit.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_frequency_unit.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.service_instructions.label }}</label>
                        {{ form.service_instructions }}
                        {% if form.service_instructions.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.service_instructions.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Provide detailed instructions for servicing this part on this machine.
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if machine_part %}{% url 'machines:detail' machine_part.machine.pk %}{% elif machine %}{% url 'machines:detail' machine.pk %}{% else %}{% url 'machines:list' %}{% endif %}" class="btn btn-secondary me-2">
                            Cancel
                        </a>
                        {% if not machine_part %}
                        <button type="submit" name="save_and_add_another" class="btn btn-outline-primary me-2">
                            <i class="fas fa-plus me-1"></i> Save & Add Another
                        </button>
                        {% endif %}
                        <button type="submit" name="save" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {{ submit_text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Card -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Service Frequency Guide
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Days:</strong> For frequent maintenance (daily, weekly)</p>
                    <p><strong>Weeks:</strong> For regular maintenance (bi-weekly, monthly)</p>
                    <p><strong>Months:</strong> For periodic maintenance (quarterly, annually)</p>
                    <hr>
                    <p><strong>Examples:</strong></p>
                    <ul>
                        <li>Oil change: 30 days</li>
                        <li>Filter replacement: 2 weeks</li>
                        <li>Annual inspection: 12 months</li>
                    </ul>
                    <hr>
                    <div class="alert alert-info alert-sm">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Auto-Scheduling:</strong> When you add a part, the system automatically creates the first service schedule based on the frequency you set.
                        </small>
                    </div>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
