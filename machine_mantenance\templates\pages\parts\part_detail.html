{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ part.part_no }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        {{ part.part_no }}
        {% if part.stock_status == 'low' %}
            <span class="badge bg-danger ms-2">Low Stock</span>
        {% elif part.stock_status == 'adequate' %}
            <span class="badge bg-warning ms-2">Adequate</span>
        {% else %}
            <span class="badge bg-success ms-2">Good Stock</span>
        {% endif %}
    </h1>
    <p class="header-subtitle">Part inventory and usage information.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Part Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Part Information</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'parts:edit' part.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                        <a href="{% url 'parts:create_order_for_part' part.pk %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-shopping-cart me-1"></i> Order Parts
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Part No:</dt>
                            <dd class="col-sm-7">{{ part.part_no }}</dd>
                            
                            <dt class="col-sm-5">Current Stock:</dt>
                            <dd class="col-sm-7">
                                <span class="badge {% if current_stock < part.minimum_stock %}bg-danger{% elif current_stock < part.minimum_stock|add:10 %}bg-warning{% else %}bg-success{% endif %}">
                                    {{ current_stock }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-5">Minimum Stock:</dt>
                            <dd class="col-sm-7">{{ part.minimum_stock }}</dd>
                            
                            <dt class="col-sm-5">Stock Status:</dt>
                            <dd class="col-sm-7">
                                {% if stock_status == 'low' %}
                                    <span class="badge bg-danger">Low Stock</span>
                                {% elif stock_status == 'adequate' %}
                                    <span class="badge bg-warning">Adequate</span>
                                {% else %}
                                    <span class="badge bg-success">Good</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Created:</dt>
                            <dd class="col-sm-7">{{ part.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Last Updated:</dt>
                            <dd class="col-sm-7">{{ part.updated_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Used In:</dt>
                            <dd class="col-sm-7">{{ machine_parts.count }} machine(s)</dd>
                        </dl>
                    </div>
                </div>
                
                {% if part.description %}
                <div class="mt-3">
                    <h6>Description:</h6>
                    <p class="text-muted">{{ part.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Stock Alert -->
    <div class="col-12 col-lg-4">
        {% if stock_status == 'low' %}
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Low Stock Alert
                </h5>
            </div>
            <div class="card-body">
                <p>Current stock ({{ current_stock }}) is below minimum level ({{ part.minimum_stock }}).</p>
                <a href="{% url 'parts:create_order_for_part' part.pk %}" class="btn btn-danger btn-sm">
                    <i class="fas fa-shopping-cart me-1"></i> Order Now
                </a>
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Stock Summary</h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-success">{{ current_stock }}</h3>
                <p class="text-muted mb-0">Units in stock</p>
                <small class="text-muted">Minimum: {{ part.minimum_stock }}</small>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Recent Orders -->
<div class="row mt-4">
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Orders</h5>
            </div>
            <div class="card-body">
                {% if part_orders %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Quantity</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in part_orders %}
                            <tr>
                                <td>{{ order.order_date|date:"M d, Y" }}</td>
                                <td><span class="badge bg-primary">{{ order.quantity }}</span></td>
                                <td>
                                    {% if order.order_reference_document %}
                                        <a href="{{ order.order_reference_document.url }}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-file"></i>
                                        </a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>No orders recorded yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Used In Machines -->
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Used In Machines</h5>
            </div>
            <div class="card-body">
                {% if machine_parts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Machine</th>
                                <th>Service Frequency</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for machine_part in machine_parts %}
                            <tr>
                                <td>
                                    <a href="{% url 'machines:detail' machine_part.machine.pk %}">
                                        {{ machine_part.machine.name }}
                                    </a>
                                </td>
                                <td>{{ machine_part.service_frequency }} {{ machine_part.get_service_frequency_unit_display|lower }}</td>
                                <td>
                                    {% if machine_part.is_overdue %}
                                        <span class="badge bg-danger">Overdue</span>
                                    {% else %}
                                        <span class="badge bg-success">Current</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-industry fa-2x mb-2"></i>
                    <p>Not used in any machines yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
