{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if part %}
            Update part information and stock settings.
        {% else %}
            Add a new part to the inventory system.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Part Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">{{ form.minimum_stock.label }}</label>
                                {{ form.minimum_stock }}
                                {% if form.minimum_stock.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.minimum_stock.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.minimum_stock.help_text %}
                                    <div class="form-text">{{ form.minimum_stock.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if part and form.created_at %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.created_at.label }}</label>
                                {{ form.created_at }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.updated_at.label }}</label>
                                {{ form.updated_at }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if part %}{% url 'parts:detail' part.pk %}{% else %}{% url 'parts:list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Card -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Stock Management Tips
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Minimum Stock Level:</strong> Set this to the lowest quantity you want to keep in inventory before reordering.</p>
                    <p><strong>Description:</strong> Include part specifications, compatibility notes, or supplier information.</p>
                    <hr>
                    <p><strong>Stock Status Colors:</strong></p>
                    <ul>
                        <li><span class="badge bg-danger">Red</span> - Below minimum</li>
                        <li><span class="badge bg-warning">Yellow</span> - Low but adequate</li>
                        <li><span class="badge bg-success">Green</span> - Good stock level</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
