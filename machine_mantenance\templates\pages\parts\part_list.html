{% extends 'base/base.html' %}
{% load static %}

{% block title %}Parts - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Parts Inventory
    </h1>
    <p class="header-subtitle">Manage parts inventory and stock levels.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Parts ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'parts:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Part
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            {{ search_form.name }}
                        </div>
                        <div class="col-md-6">
                            {{ search_form.stock_status }}
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'parts:list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Parts Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Part Name</th>
                                <th>Current Stock</th>
                                <th>Minimum Stock</th>
                                <th>Status</th>
                                <th>Used In Machines</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for part in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ part.name }}</strong>
                                    {% if part.description %}
                                        <br><small class="text-muted">{{ part.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge {% if part.current_stock < part.minimum_stock %}bg-danger{% elif part.current_stock < part.minimum_stock|add:10 %}bg-warning{% else %}bg-success{% endif %}">
                                        {{ part.current_stock }}
                                    </span>
                                </td>
                                <td>{{ part.minimum_stock }}</td>
                                <td>
                                    {% if part.stock_status == 'low' %}
                                        <span class="badge bg-danger">Low Stock</span>
                                    {% elif part.stock_status == 'adequate' %}
                                        <span class="badge bg-warning">Adequate</span>
                                    {% else %}
                                        <span class="badge bg-success">Good</span>
                                    {% endif %}
                                </td>
                                <td>{{ part.part_machines.count }}</td>
                                <td>
                                    <a href="{% url 'parts:detail' part.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'parts:edit' part.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'parts:create_order_for_part' part.pk %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="fas fa-cubes fa-3x mb-3"></i>
                                    <p>No parts found.</p>
                                    <a href="{% url 'parts:create' %}" class="btn btn-primary">Add First Part</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Parts pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.stock_status %}&stock_status={{ request.GET.stock_status }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.stock_status %}&stock_status={{ request.GET.stock_status }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.stock_status %}&stock_status={{ request.GET.stock_status }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
