{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if part %}
            Order more units of {{ part.part_no }}.
        {% else %}
            Record a new part order for inventory tracking.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Order Details</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.part.label }}</label>
                                {{ form.part }}
                                {% if form.part.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.part.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.quantity.label }}</label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.order_date.label }}</label>
                                {{ form.order_date }}
                                {% if form.order_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.order_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.order_reference_document.label }}</label>
                                {{ form.order_reference_document }}
                                {% if form.order_reference_document.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.order_reference_document.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Upload purchase order, receipt, or invoice (optional)
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if part %}{% url 'parts:detail' part.pk %}{% else %}{% url 'parts:list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Order Summary -->
    {% if part %}
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Part Information
                </h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-6">Part:</dt>
                    <dd class="col-sm-6">{{ part.part_no }}</dd>
                    
                    <dt class="col-sm-6">Current Stock:</dt>
                    <dd class="col-sm-6">
                        <span class="badge {% if part.current_stock < part.minimum_stock %}bg-danger{% elif part.current_stock < part.minimum_stock|add:10 %}bg-warning{% else %}bg-success{% endif %}">
                            {{ part.current_stock }}
                        </span>
                    </dd>
                    
                    <dt class="col-sm-6">Minimum:</dt>
                    <dd class="col-sm-6">{{ part.minimum_stock }}</dd>
                    
                    <dt class="col-sm-6">Status:</dt>
                    <dd class="col-sm-6">
                        {% if part.stock_status == 'low' %}
                            <span class="badge bg-danger">Low Stock</span>
                        {% elif part.stock_status == 'adequate' %}
                            <span class="badge bg-warning">Adequate</span>
                        {% else %}
                            <span class="badge bg-success">Good</span>
                        {% endif %}
                    </dd>
                </dl>
                
                {% if part.stock_status == 'low' %}
                <div class="alert alert-warning alert-sm">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        This part is currently below minimum stock level.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
