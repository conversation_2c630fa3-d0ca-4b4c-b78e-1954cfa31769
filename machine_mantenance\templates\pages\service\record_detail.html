{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Record #{{ record.pk }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Record #{{ record.pk }}
        {% if record.ticket_item %}
            <span class="badge bg-info ms-2">Scheduled</span>
        {% else %}
            <span class="badge bg-secondary ms-2">Manual</span>
        {% endif %}
    </h1>
    <p class="header-subtitle">Detailed service record and maintenance information.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Service Record Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Service Details</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'service:record_edit' record.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Record #:</dt>
                            <dd class="col-sm-7"><strong>#{{ record.pk }}</strong></dd>
                            
                            <dt class="col-sm-5">Service Date:</dt>
                            <dd class="col-sm-7">{{ record.service_date|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Machine:</dt>
                            <dd class="col-sm-7">
                                <a href="{% url 'machines:detail' record.machine_part.machine.pk %}">
                                    {{ record.machine_part.machine.name }}
                                </a>
                                <br><small class="text-muted">{{ record.machine_part.machine.tag_number }}</small>
                            </dd>
                            
                            <dt class="col-sm-5">Part:</dt>
                            <dd class="col-sm-7">{{ record.machine_part.part.name }}</dd>
                            
                            <dt class="col-sm-5">Technician:</dt>
                            <dd class="col-sm-7">
                                <a href="{% url 'technicians:detail' record.technician.pk %}">
                                    {{ record.technician.name }}
                                </a>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Parts Used:</dt>
                            <dd class="col-sm-7">
                                {% if record.number_of_parts_replaced > 0 %}
                                    <span class="badge bg-primary">{{ record.number_of_parts_replaced }}</span>
                                {% else %}
                                    <span class="text-muted">None</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Service Type:</dt>
                            <dd class="col-sm-7">
                                {% if record.ticket_item %}
                                    <span class="badge bg-info">Scheduled Service</span>
                                    {% if record.ticket_item.service_ticket %}
                                        <br><small>
                                            <a href="{% url 'service:ticket_detail' record.ticket_item.service_ticket.pk %}">
                                                Ticket #{{ record.ticket_item.service_ticket.pk }}
                                            </a>
                                        </small>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">Manual Service</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-5">Created:</dt>
                            <dd class="col-sm-7">{{ record.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Last Updated:</dt>
                            <dd class="col-sm-7">{{ record.updated_at|date:"M d, Y" }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Service Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Service Instructions</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">{{ record.machine_part.service_instructions }}</p>
                <small class="text-muted">
                    <strong>Service Frequency:</strong> 
                    Every {{ record.machine_part.service_frequency }} {{ record.machine_part.get_service_frequency_unit_display|lower }}
                </small>
            </div>
        </div>
        
        <!-- Comments -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Comments</h5>
            </div>
            <div class="card-body">
                {% if record.technician_comments %}
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-user-cog me-2"></i>Technician Comments
                    </h6>
                    <p class="text-muted">{{ record.technician_comments }}</p>
                </div>
                {% endif %}
                
                {% if record.supervisor_comments %}
                <div class="mb-0">
                    <h6 class="text-success">
                        <i class="fas fa-user-tie me-2"></i>Supervisor Comments
                    </h6>
                    <p class="text-muted">{{ record.supervisor_comments }}</p>
                </div>
                {% endif %}
                
                {% if not record.technician_comments and not record.supervisor_comments %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-comment fa-2x mb-2"></i>
                    <p>No comments recorded.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions & Info -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'service:record_edit' record.pk %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Record
                    </a>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>Print Record
                    </button>
                    <a href="{% url 'service:record_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Records
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Related Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Related Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Machine Location</h6>
                    <p class="text-muted mb-0">{{ record.machine_part.machine.location }}</p>
                </div>
                
                <div class="mb-3">
                    <h6>Part Stock Status</h6>
                    <p class="mb-0">
                        {% if record.machine_part.part.stock_status == 'low' %}
                            <span class="badge bg-danger">Low Stock</span>
                        {% elif record.machine_part.part.stock_status == 'adequate' %}
                            <span class="badge bg-warning">Adequate</span>
                        {% else %}
                            <span class="badge bg-success">Good</span>
                        {% endif %}
                    </p>
                    <small class="text-muted">
                        Current: {{ record.machine_part.part.current_stock }} | 
                        Min: {{ record.machine_part.part.minimum_stock }}
                    </small>
                </div>
                
                <div class="mb-0">
                    <h6>Next Service Due</h6>
                    <p class="text-muted mb-0">{{ record.machine_part.get_next_service_date|date:"M d, Y" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
