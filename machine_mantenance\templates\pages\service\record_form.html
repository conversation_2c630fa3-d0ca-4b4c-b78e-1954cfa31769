{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if record %}
            Update service record information and comments.
        {% else %}
            Record completed maintenance work and service details.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Service Record Details</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <!-- Service Ticket Item (if applicable) -->
                    {% if form.ticket_item %}
                    <div class="mb-3">
                        <label class="form-label">{{ form.ticket_item.label }}</label>
                        {{ form.ticket_item }}
                        {% if form.ticket_item.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.ticket_item.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Link to service ticket item if this was a scheduled service.
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.machine_part.label }}</label>
                                {{ form.machine_part }}
                                {% if form.machine_part.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.machine_part.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.technician.label }}</label>
                                {{ form.technician }}
                                {% if form.technician.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.technician.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.service_date.label }}</label>
                                {{ form.service_date }}
                                {% if form.service_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.number_of_parts_replaced.label }}</label>
                                {{ form.number_of_parts_replaced }}
                                {% if form.number_of_parts_replaced.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.number_of_parts_replaced.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Number of parts consumed during this service.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.technician_comments.label }}</label>
                        {{ form.technician_comments }}
                        {% if form.technician_comments.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.technician_comments.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Comments from the technician who performed the service.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.supervisor_comments.label }}</label>
                        {{ form.supervisor_comments }}
                        {% if form.supervisor_comments.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.supervisor_comments.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Comments from the supervisor after inspection.
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if record %}{% url 'service:record_detail' record.pk %}{% else %}{% url 'service:record_list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                        {% if not record %}
                        <button type="submit" name="schedule_next" class="btn btn-outline-success ms-2">
                            <i class="fas fa-calendar-plus me-1"></i> Save & Schedule Next
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Service Instructions & Help -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Service Record Guide
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Machine Part:</strong> Select the specific machine part that was serviced.</p>
                    <p><strong>Service Date:</strong> The actual date when the service was performed.</p>
                    <p><strong>Parts Replaced:</strong> Enter the number of parts consumed during service (affects inventory).</p>
                    <p><strong>Comments:</strong></p>
                    <ul>
                        <li><strong>Technician:</strong> Work performed, issues found, recommendations</li>
                        <li><strong>Supervisor:</strong> Quality check, approval, additional notes</li>
                    </ul>
                    <hr>
                    <p><strong>"Save & Schedule Next"</strong> will automatically create the next service schedule based on the part's service frequency.</p>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
