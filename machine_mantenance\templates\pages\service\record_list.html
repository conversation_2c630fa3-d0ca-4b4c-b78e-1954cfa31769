{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Records - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Records
    </h1>
    <p class="header-subtitle">Track completed maintenance work and service history.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Service Records ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'service:record_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Record
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            {{ search_form.machine_name }}
                        </div>
                        <div class="col-md-3">
                            {{ search_form.part_name }}
                        </div>
                        <div class="col-md-3">
                            {{ search_form.technician_name }}
                        </div>
                        <div class="col-md-3">
                            <div class="row g-2">
                                <div class="col-6">
                                    {{ search_form.date_from }}
                                </div>
                                <div class="col-6">
                                    {{ search_form.date_to }}
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'service:record_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Service Records Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Service Date</th>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Technician</th>
                                <th>Parts Used</th>
                                <th>Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in page_obj %}
                            <tr>
                                <td>{{ record.service_date|date:"M d, Y" }}</td>
                                <td>
                                    <a href="{% url 'machines:detail' record.machine_part.machine.pk %}">
                                        {{ record.machine_part.machine.name }}
                                    </a>
                                    <br><small class="text-muted">{{ record.machine_part.machine.tag_number }}</small>
                                </td>
                                <td>{{ record.machine_part.part.part_no }}</td>
                                <td>
                                    <a href="{% url 'technicians:detail' record.technician.pk %}">
                                        {{ record.technician.name }}
                                    </a>
                                </td>
                                <td>
                                    {% if record.number_of_parts_replaced > 0 %}
                                        <span class="badge bg-primary">{{ record.number_of_parts_replaced }}</span>
                                    {% else %}
                                        <span class="text-muted">None</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.ticket_item %}
                                        <span class="badge bg-info">Scheduled</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Manual</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'service:record_detail' record.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'service:record_edit' record.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                    <p>No service records found.</p>
                                    <a href="{% url 'service:record_create' %}" class="btn btn-primary">Add First Record</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Service records pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
