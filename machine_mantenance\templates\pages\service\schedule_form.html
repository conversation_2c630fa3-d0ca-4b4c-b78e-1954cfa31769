{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">Schedule maintenance service for a machine part.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Schedule Details</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.machine_part.label }}</label>
                        {{ form.machine_part }}
                        {% if form.machine_part.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.machine_part.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Select the machine part that needs service.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.scheduled_date.label }}</label>
                                {{ form.scheduled_date }}
                                {% if form.scheduled_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.scheduled_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.assigned_technician.label }}</label>
                                {{ form.assigned_technician }}
                                {% if form.assigned_technician.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.assigned_technician.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Optional - can be assigned later.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'service:schedule_list' %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Card -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Scheduling Tips
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Machine Part:</strong> Each machine part has its own service frequency defined in the machine configuration.</p>
                    <p><strong>Scheduled Date:</strong> Choose a date when the service should be performed.</p>
                    <p><strong>Technician Assignment:</strong> You can assign a specific technician or leave it unassigned for later assignment.</p>
                    <hr>
                    <p><strong>Status Colors:</strong></p>
                    <ul>
                        <li><span class="badge bg-danger">Red</span> - Overdue</li>
                        <li><span class="badge bg-warning">Yellow</span> - Due today</li>
                        <li><span class="badge bg-success">Green</span> - Upcoming</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
