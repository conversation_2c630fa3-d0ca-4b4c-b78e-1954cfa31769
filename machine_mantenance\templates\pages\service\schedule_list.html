{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Schedule - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Schedule
    </h1>
    <p class="header-subtitle">Manage maintenance schedules and track service due dates.</p>
</div>
{% endblock %}

{% block content %}
<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-sm-6 col-xl-3">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col mt-0">
                        <h5 class="card-title">Overdue</h5>
                    </div>
                    <div class="col-auto">
                        <div class="stat text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
                <h1 class="mt-1 mb-3 text-danger">{{ overdue|length }}</h1>
                <div class="mb-0">
                    <span class="text-muted">Services past due</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col mt-0">
                        <h5 class="card-title">Due Today</h5>
                    </div>
                    <div class="col-auto">
                        <div class="stat text-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
                <h1 class="mt-1 mb-3 text-warning">{{ due_today|length }}</h1>
                <div class="mb-0">
                    <span class="text-muted">Services due today</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col mt-0">
                        <h5 class="card-title">Upcoming</h5>
                    </div>
                    <div class="col-auto">
                        <div class="stat text-success">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                    </div>
                </div>
                <h1 class="mt-1 mb-3 text-success">{{ upcoming|length }}</h1>
                <div class="mb-0">
                    <span class="text-muted">Future services</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xl-3">
        <div class="card">
            <div class="card-body text-center">
                <a href="{% url 'service:schedule_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Schedule Service
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Services -->
{% if overdue %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Overdue Services ({{ overdue|length }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Scheduled Date</th>
                                <th>Days Overdue</th>
                                <th>Technician</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in overdue %}
                            <tr>
                                <td>
                                    <a href="{% url 'machines:detail' schedule.machine_part.machine.pk %}">
                                        {{ schedule.machine_part.machine.name }}
                                    </a>
                                </td>
                                <td>{{ schedule.machine_part.part.part_no }}</td>
                                <td>{{ schedule.scheduled_date|date:"M d, Y" }}</td>
                                <td>
                                    <span class="badge bg-danger">
                                        {{ schedule.scheduled_date|timesince|cut:" ago" }}
                                    </span>
                                </td>
                                <td>
                                    {% if schedule.assigned_technician %}
                                        <a href="{% url 'technicians:detail' schedule.assigned_technician.pk %}">
                                            {{ schedule.assigned_technician.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Unassigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'service:record_create' %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-wrench"></i> Service Now
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Due Today -->
{% if due_today %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Due Today ({{ due_today|length }})
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Technician</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in due_today %}
                            <tr>
                                <td>
                                    <a href="{% url 'machines:detail' schedule.machine_part.machine.pk %}">
                                        {{ schedule.machine_part.machine.name }}
                                    </a>
                                </td>
                                <td>{{ schedule.machine_part.part.part_no }}</td>
                                <td>
                                    {% if schedule.assigned_technician %}
                                        <a href="{% url 'technicians:detail' schedule.assigned_technician.pk %}">
                                            {{ schedule.assigned_technician.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Unassigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'service:record_create' %}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-wrench"></i> Service
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Upcoming Services -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    Upcoming Services ({{ upcoming|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if upcoming %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Scheduled Date</th>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Technician</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in upcoming %}
                            <tr>
                                <td>{{ schedule.scheduled_date|date:"M d, Y" }}</td>
                                <td>
                                    <a href="{% url 'machines:detail' schedule.machine_part.machine.pk %}">
                                        {{ schedule.machine_part.machine.name }}
                                    </a>
                                </td>
                                <td>{{ schedule.machine_part.part.part_no }}</td>
                                <td>
                                    {% if schedule.assigned_technician %}
                                        <a href="{% url 'technicians:detail' schedule.assigned_technician.pk %}">
                                            {{ schedule.assigned_technician.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Unassigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'service:record_create' %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-wrench"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-check fa-3x mb-3"></i>
                    <p>No upcoming services scheduled.</p>
                    <a href="{% url 'service:schedule_create' %}" class="btn btn-primary">Schedule First Service</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
