{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Ticket #{{ ticket.pk }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Ticket #{{ ticket.pk }}
        {% if ticket.service_date < today %}
            {% if ticket.ticket_items.exists %}
                <span class="badge bg-success ms-2">Completed</span>
            {% else %}
                <span class="badge bg-warning ms-2">In Progress</span>
            {% endif %}
        {% elif ticket.service_date == today %}
            <span class="badge bg-info ms-2">Due Today</span>
        {% else %}
            <span class="badge bg-secondary ms-2">Scheduled</span>
        {% endif %}
    </h1>
    <p class="header-subtitle">Service ticket details and work items.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Ticket Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Ticket Information</h5>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-print me-1"></i> Print Worksheet
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'service:record_create' %}?ticket={{ ticket.pk }}">
                                    <i class="fas fa-wrench me-2"></i>Add Service Record
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#">
                                    <i class="fas fa-trash me-2"></i>Delete Ticket
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Ticket #:</dt>
                            <dd class="col-sm-7"><strong>#{{ ticket.pk }}</strong></dd>
                            
                            <dt class="col-sm-5">Service Date:</dt>
                            <dd class="col-sm-7">{{ ticket.service_date|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Assigned To:</dt>
                            <dd class="col-sm-7">
                                {% if ticket.assigned_technician %}
                                    <a href="{% url 'technicians:detail' ticket.assigned_technician.pk %}">
                                        {{ ticket.assigned_technician.name }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">Unassigned</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Created:</dt>
                            <dd class="col-sm-7">{{ ticket.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Last Updated:</dt>
                            <dd class="col-sm-7">{{ ticket.updated_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Items:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-primary">{{ ticket_items.count }}</span>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'service:record_create' %}?ticket={{ ticket.pk }}" class="btn btn-primary">
                        <i class="fas fa-wrench me-2"></i>Add Service Record
                    </a>
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>Print Worksheet
                    </button>
                    <a href="{% url 'service:ticket_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tickets
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ticket Items -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Service Items ({{ ticket_items.count }})</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Add Item
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if ticket_items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Service Instructions</th>
                                <th>Referenced Schedule</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in ticket_items %}
                            <tr>
                                <td>
                                    <a href="{% url 'machines:detail' item.machine_part.machine.pk %}">
                                        {{ item.machine_part.machine.name }}
                                    </a>
                                    <br><small class="text-muted">{{ item.machine_part.machine.tag_number }}</small>
                                </td>
                                <td>{{ item.machine_part.part.name }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ item.machine_part.service_instructions|truncatechars:100 }}
                                    </small>
                                </td>
                                <td>
                                    {% if item.referenced_schedule %}
                                        <span class="badge bg-info">Scheduled</span>
                                        <br><small>{{ item.referenced_schedule.scheduled_date|date:"M d" }}</small>
                                    {% else %}
                                        <span class="text-muted">Manual</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.service_records.exists %}
                                        <span class="badge bg-success">Completed</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if not item.service_records.exists %}
                                        <a href="{% url 'service:record_create' %}?ticket_item={{ item.pk }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-wrench"></i> Service
                                        </a>
                                    {% else %}
                                        <a href="{% url 'service:record_detail' item.service_records.first.pk %}" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-list fa-3x mb-3"></i>
                    <p>No items added to this ticket yet.</p>
                    <button class="btn btn-primary">Add First Item</button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
