{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">Create a new service ticket for maintenance work.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Ticket Details</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.service_date.label }}</label>
                                {{ form.service_date }}
                                {% if form.service_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.service_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Date when the service work should be performed.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.assigned_technician.label }}</label>
                                {{ form.assigned_technician }}
                                {% if form.assigned_technician.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.assigned_technician.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Optional - can be assigned later.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'service:ticket_list' %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Workflow Guide -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Service Ticket Workflow
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <div class="mb-3">
                        <h6>1. Create Ticket</h6>
                        <p>Set the service date and assign a technician.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>2. Add Items</h6>
                        <p>Add machine parts that need service to the ticket.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>3. Print Worksheet</h6>
                        <p>Generate a printable worksheet for the technician.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>4. Complete Service</h6>
                        <p>Technician performs work and fills out the worksheet.</p>
                    </div>
                    
                    <div class="mb-0">
                        <h6>5. Record Results</h6>
                        <p>Enter service records with parts used and comments.</p>
                    </div>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
