{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Tickets - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Tickets
    </h1>
    <p class="header-subtitle">Manage service tickets and work orders.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Service Tickets ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'service:ticket_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Create Ticket
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Service Tickets Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Service Date</th>
                                <th>Assigned Technician</th>
                                <th>Items</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ticket in page_obj %}
                            <tr>
                                <td>
                                    <strong>#{{ ticket.pk }}</strong>
                                </td>
                                <td>{{ ticket.service_date|date:"M d, Y" }}</td>
                                <td>
                                    {% if ticket.assigned_technician %}
                                        <a href="{% url 'technicians:detail' ticket.assigned_technician.pk %}">
                                            {{ ticket.assigned_technician.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Unassigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ ticket.ticket_items.count }}</span>
                                </td>
                                <td>
                                    {% if ticket.service_date < today %}
                                        {% if ticket.ticket_items.exists %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-warning">In Progress</span>
                                        {% endif %}
                                    {% elif ticket.service_date == today %}
                                        <span class="badge bg-info">Due Today</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Scheduled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'service:ticket_detail' ticket.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{% url 'service:record_create' %}?ticket={{ ticket.pk }}">
                                                <i class="fas fa-wrench me-2"></i>Add Service Record
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-primary" href="#">
                                                <i class="fas fa-print me-2"></i>Print Worksheet
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                                    <p>No service tickets found.</p>
                                    <a href="{% url 'service:ticket_create' %}" class="btn btn-primary">Create First Ticket</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Service tickets pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
