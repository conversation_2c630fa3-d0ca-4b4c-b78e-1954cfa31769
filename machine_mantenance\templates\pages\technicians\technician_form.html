{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if technician %}
            Update technician information and contact details.
        {% else %}
            Add a new technician to the maintenance team.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Technician Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.email.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.specialization.label }}</label>
                        {{ form.specialization }}
                        {% if form.specialization.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.specialization.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            List the technician's skills, certifications, and areas of expertise.
                        </div>
                    </div>
                    
                    {% if technician and form.created_at %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.created_at.label }}</label>
                                {{ form.created_at }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.updated_at.label }}</label>
                                {{ form.updated_at }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if technician %}{% url 'technicians:detail' technician.pk %}{% else %}{% url 'technicians:list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Card -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Technician Guidelines
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Contact Information:</strong> Email and phone are used for notifications and scheduling.</p>
                    <p><strong>Specialization Examples:</strong></p>
                    <ul>
                        <li>Electrical systems</li>
                        <li>Hydraulic maintenance</li>
                        <li>Mechanical repairs</li>
                        <li>Preventive maintenance</li>
                        <li>Safety inspections</li>
                    </ul>
                    <hr>
                    <p><strong>Certifications:</strong> Include any relevant certifications or training completed.</p>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
