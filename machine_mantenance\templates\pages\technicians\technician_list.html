{% extends 'base/base.html' %}
{% load static %}

{% block title %}Technicians - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Technicians
    </h1>
    <p class="header-subtitle">Manage maintenance technicians and their specializations.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">All Technicians ({{ total_count }})</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'technicians:create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Technician
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            {{ search_form.name }}
                        </div>
                        <div class="col-md-6">
                            {{ search_form.specialization }}
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'technicians:list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Technicians Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Specialization</th>
                                <th>Recent Services</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for technician in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ technician.name }}</strong>
                                </td>
                                <td>
                                    {% if technician.email %}
                                        <div><i class="fas fa-envelope me-1"></i> {{ technician.email }}</div>
                                    {% endif %}
                                    {% if technician.phone %}
                                        <div><i class="fas fa-phone me-1"></i> {{ technician.phone }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ technician.specialization|truncatechars:50 }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ technician.service_records.count }}</span>
                                </td>
                                <td>
                                    <a href="{% url 'technicians:detail' technician.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'technicians:edit' technician.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-user-tag fa-3x mb-3"></i>
                                    <p>No technicians found.</p>
                                    <a href="{% url 'technicians:create' %}" class="btn btn-primary">Add First Technician</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Technicians pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.specialization %}&specialization={{ request.GET.specialization }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.specialization %}&specialization={{ request.GET.specialization }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.name %}&name={{ request.GET.name }}{% endif %}{% if request.GET.specialization %}&specialization={{ request.GET.specialization }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
