{% extends 'base/base.html' %}
{% load static %}

{% block title %}Alerts - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">Alerts</h1>
    <p class="header-subtitle">Provide contextual feedback messages for typical user actions.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Default alerts</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary" role="alert">
                    A simple primary alert—check it out!
                </div>
                <div class="alert alert-secondary" role="alert">
                    A simple secondary alert—check it out!
                </div>
                <div class="alert alert-success" role="alert">
                    A simple success alert—check it out!
                </div>
                <div class="alert alert-danger" role="alert">
                    A simple danger alert—check it out!
                </div>
                <div class="alert alert-warning" role="alert">
                    A simple warning alert—check it out!
                </div>
                <div class="alert alert-info" role="alert">
                    A simple info alert—check it out!
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Dismissible alerts</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary alert-dismissible" role="alert">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    A simple primary alert with close button.
                </div>
                <div class="alert alert-success alert-dismissible" role="alert">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    A simple success alert with close button.
                </div>
                <div class="alert alert-danger alert-dismissible" role="alert">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    A simple danger alert with close button.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Alerts with icons</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success" role="alert">
                    <div class="alert-icon">
                        <i class="align-middle" data-feather="check-circle"></i>
                    </div>
                    <div class="alert-message">
                        <strong>Well done!</strong> You successfully read this important alert message.
                    </div>
                </div>
                <div class="alert alert-info" role="alert">
                    <div class="alert-icon">
                        <i class="align-middle" data-feather="info"></i>
                    </div>
                    <div class="alert-message">
                        <strong>Heads up!</strong> This alert needs your attention, but it's not super important.
                    </div>
                </div>
                <div class="alert alert-warning" role="alert">
                    <div class="alert-icon">
                        <i class="align-middle" data-feather="alert-triangle"></i>
                    </div>
                    <div class="alert-message">
                        <strong>Warning!</strong> Better check yourself, you're not looking too good.
                    </div>
                </div>
                <div class="alert alert-danger" role="alert">
                    <div class="alert-icon">
                        <i class="align-middle" data-feather="alert-circle"></i>
                    </div>
                    <div class="alert-message">
                        <strong>Oh snap!</strong> Change a few things up and try submitting again.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
