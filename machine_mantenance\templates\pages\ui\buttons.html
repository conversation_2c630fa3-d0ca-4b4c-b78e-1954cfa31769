{% extends 'base/base.html' %}
{% load static %}

{% block title %}Buttons - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Buttons
    </h1>
    <p class="header-subtitle">Use Bootstrap's custom button styles for actions in forms, dialogs, and more.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Default buttons</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary">Primary</button>
                <button type="button" class="btn btn-secondary">Secondary</button>
                <button type="button" class="btn btn-success">Success</button>
                <button type="button" class="btn btn-danger">Danger</button>
                <button type="button" class="btn btn-warning">Warning</button>
                <button type="button" class="btn btn-info">Info</button>
                <button type="button" class="btn btn-light">Light</button>
                <button type="button" class="btn btn-dark">Dark</button>
                <button type="button" class="btn btn-link">Link</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Outline buttons</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-outline-primary">Primary</button>
                <button type="button" class="btn btn-outline-secondary">Secondary</button>
                <button type="button" class="btn btn-outline-success">Success</button>
                <button type="button" class="btn btn-outline-danger">Danger</button>
                <button type="button" class="btn btn-outline-warning">Warning</button>
                <button type="button" class="btn btn-outline-info">Info</button>
                <button type="button" class="btn btn-outline-light">Light</button>
                <button type="button" class="btn btn-outline-dark">Dark</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Button sizes</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary btn-lg">Large button</button>
                <button type="button" class="btn btn-secondary btn-lg">Large button</button>
                <br><br>
                <button type="button" class="btn btn-primary">Default button</button>
                <button type="button" class="btn btn-secondary">Default button</button>
                <br><br>
                <button type="button" class="btn btn-primary btn-sm">Small button</button>
                <button type="button" class="btn btn-secondary btn-sm">Small button</button>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Button states</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary">Normal</button>
                <button type="button" class="btn btn-primary active">Active</button>
                <button type="button" class="btn btn-primary" disabled>Disabled</button>
                <br><br>
                <button type="button" class="btn btn-outline-primary">Normal</button>
                <button type="button" class="btn btn-outline-primary active">Active</button>
                <button type="button" class="btn btn-outline-primary" disabled>Disabled</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Button with icons</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary">
                    <i data-feather="home"></i> Home
                </button>
                <button type="button" class="btn btn-success">
                    <i data-feather="check"></i> Success
                </button>
                <button type="button" class="btn btn-danger">
                    <i data-feather="x"></i> Delete
                </button>
                <button type="button" class="btn btn-warning">
                    <i data-feather="alert-triangle"></i> Warning
                </button>
                <br><br>
                <button type="button" class="btn btn-outline-primary">
                    <i data-feather="download"></i> Download
                </button>
                <button type="button" class="btn btn-outline-success">
                    <i data-feather="save"></i> Save
                </button>
                <button type="button" class="btn btn-outline-info">
                    <i data-feather="info"></i> Info
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Block buttons</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="button">Block level button</button>
                    <button class="btn btn-secondary" type="button">Block level button</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Button groups</h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group" aria-label="Basic example">
                    <button type="button" class="btn btn-secondary">Left</button>
                    <button type="button" class="btn btn-secondary">Middle</button>
                    <button type="button" class="btn btn-secondary">Right</button>
                </div>
                <br><br>
                <div class="btn-group" role="group" aria-label="Basic outlined example">
                    <button type="button" class="btn btn-outline-primary">Left</button>
                    <button type="button" class="btn btn-outline-primary">Middle</button>
                    <button type="button" class="btn btn-outline-primary">Right</button>
                </div>
                <br><br>
                <div class="btn-group" role="group" aria-label="Mixed styles">
                    <button type="button" class="btn btn-danger">Left</button>
                    <button type="button" class="btn btn-warning">Middle</button>
                    <button type="button" class="btn btn-success">Right</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Button toolbar</h5>
            </div>
            <div class="card-body">
                <div class="btn-toolbar" role="toolbar" aria-label="Toolbar with button groups">
                    <div class="btn-group me-2" role="group" aria-label="First group">
                        <button type="button" class="btn btn-outline-secondary">1</button>
                        <button type="button" class="btn btn-outline-secondary">2</button>
                        <button type="button" class="btn btn-outline-secondary">3</button>
                        <button type="button" class="btn btn-outline-secondary">4</button>
                    </div>
                    <div class="btn-group me-2" role="group" aria-label="Second group">
                        <button type="button" class="btn btn-outline-secondary">5</button>
                        <button type="button" class="btn btn-outline-secondary">6</button>
                        <button type="button" class="btn btn-outline-secondary">7</button>
                    </div>
                    <div class="btn-group" role="group" aria-label="Third group">
                        <button type="button" class="btn btn-outline-secondary">8</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
