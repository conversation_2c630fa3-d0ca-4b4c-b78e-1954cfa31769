<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content="Modern, flexible and responsive Bootstrap 5 admin &amp; dashboard template">
	<meta name="author" content="Bootlab">

	<title>Spark - Bootstrap 5 Admin &amp; Dashboard Template</title>

	<!-- PICK ONE OF THE STYLES BELOW -->
	<!-- <link href="css/modern.css" rel="stylesheet"> -->
	<!-- <link href="css/classic.css" rel="stylesheet"> -->
	<!-- <link href="css/dark.css" rel="stylesheet"> -->
	<!-- <link href="css/light.css" rel="stylesheet"> -->

	<!-- BEGIN SETTINGS -->
	<!-- You can remove this after picking a style -->
	<style>
		body {
			opacity: 0;
		}
	</style>
	<script src="js/settings.js"></script>
	<!-- END SETTINGS -->
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-120946860-7"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-120946860-7');
</script></head>

<body>
	<div class="splash active">
		<div class="splash-icon"></div>
	</div>

	<div class="wrapper">
		<nav id="sidebar" class="sidebar">
			<a class='sidebar-brand' href='/'>
				<svg>
					<use xlink:href="#ion-ios-pulse-strong"></use>
				</svg>
				Spark
			</a>
			<div class="sidebar-content">
				<div class="sidebar-user">
					<img src="img/avatars/avatar.jpg" class="img-fluid rounded-circle mb-2" alt="Linda Miller" />
					<div class="fw-bold">Linda Miller</div>
					<small>Front-end Developer</small>
				</div>

				<ul class="sidebar-nav">
					<li class="sidebar-header">
						Main
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#dashboards" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-home"></i> <span class="align-middle">Dashboards</span>
						</a>
						<ul id="dashboards" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/dashboard-default'>Default</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/dashboard-analytics'>Analytics</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/dashboard-e-commerce'>E-commerce</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#pages" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-file"></i> <span class="align-middle">Pages</span>
						</a>
						<ul id="pages" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-settings'>Settings</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-clients'>Clients <span
										class="sidebar-badge badge rounded-pill bg-primary">New</span></a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-invoice'>Invoice</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-pricing'>Pricing</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-tasks'>Tasks</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-chat'>Chat <span
										class="sidebar-badge badge rounded-pill bg-primary">New</span></a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-blank'>Blank Page</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#auth" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-sign-in-alt"></i> <span class="align-middle">Auth</span>
						</a>
						<ul id="auth" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-sign-in'>Sign
									In</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-sign-up'>Sign
									Up</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-reset-password'>Reset Password</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-404'>404
									Page</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/pages-500'>500
									Page</a></li>
						</ul>
					</li>

					<li class="sidebar-header">
						Elements
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#ui" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-flask"></i> <span class="align-middle">User Interface</span>
						</a>
						<ul id="ui" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-alerts'>Alerts</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-buttons'>Buttons</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-cards'>Cards</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-general'>General</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-grid'>Grid</a>
							</li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-modals'>Modals</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-offcanvas'>Offcanvas</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-placeholders'>Placeholders</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-notifications'>Notifications</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-tabs'>Tabs</a>
							</li>
							<li class="sidebar-item"><a class='sidebar-link' href='/ui-typography'>Typography</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#charts" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-chart-pie"></i> <span class="align-middle">Charts</span>
							<span class="sidebar-badge badge rounded-pill bg-primary">New</span>
						</a>
						<ul id="charts" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/charts-chartjs'>Chart.js</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/charts-apexcharts'>ApexCharts</a></li>
						</ul>
					</li>

					<li class="sidebar-item active">
						<a data-bs-target="#forms" data-bs-toggle="collapse" class="sidebar-link">
							<i class="align-middle me-2 fas fa-fw fa-check-square"></i> <span class="align-middle">Forms</span>
						</a>
						<ul id="forms" class="sidebar-dropdown list-unstyled collapse show" data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-layouts'>Layouts</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-basic-elements'>Basic Elements</a></li>
							<li class="sidebar-item active"><a class='sidebar-link' href='/forms-advanced-elements'>Advanced Elements</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-floating-labels'>Floating Labels</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-input-groups'>Input Groups</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-editors'>Editors</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-validation'>Validation</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/forms-wizard'>Wizard</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a class='sidebar-link' href='/tables-bootstrap'>
							<i class="align-middle me-2 fas fa-fw fa-list"></i> <span class="align-middle">Tables</span>
						</a>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#datatables" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-table"></i> <span class="align-middle">DataTables</span>
						</a>
						<ul id="datatables" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-responsive'>Responsive Table</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-buttons'>Table with Buttons</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-column-search'>Column Search</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-fixed-header'>Fixed Header</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-multi'>Multi Selection</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/tables-datatables-ajax'>Ajax Sourced Data</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#icons" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-heart"></i> <span class="align-middle">Icons</span>
						</a>
						<ul id="icons" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/icons-feather'>Feather</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/icons-ion'>Ion
									Icons</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/icons-font-awesome'>Font Awesome</a></li>
						</ul>
					</li>
					<li class="sidebar-item">
						<a class='sidebar-link' href='/calendar'>
							<i class="align-middle me-2 far fa-fw fa-calendar-alt"></i> <span class="align-middle">Calendar</span>
						</a>
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#maps" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-map-marker-alt"></i> <span class="align-middle">Maps</span>
						</a>
						<ul id="maps" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/maps-google'>Google Maps</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/maps-vector'>Vector Maps</a></li>
						</ul>
					</li>

					<li class="sidebar-header">
						Extras
					</li>
					<li class="sidebar-item">
						<a data-bs-target="#documentation" data-bs-toggle="collapse" class="sidebar-link collapsed">
							<i class="align-middle me-2 fas fa-fw fa-book"></i> <span class="align-middle">Documentation</span>
						</a>
						<ul id="documentation" class="sidebar-dropdown list-unstyled collapse " data-bs-parent="#sidebar">
							<li class="sidebar-item"><a class='sidebar-link' href='/docs-getting-started'>Getting Started</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/docs-plugins'>Plugins</a></li>
							<li class="sidebar-item"><a class='sidebar-link' href='/docs-changelog'>Changelog</a></li>
						</ul>
					</li>
				</ul>
			</div>
		</nav>
		<div class="main">
			<nav class="navbar navbar-expand navbar-theme">
				<a class="sidebar-toggle d-flex me-2">
					<i class="hamburger align-self-center"></i>
				</a>

				<form class="d-none d-sm-inline-block">
					<input class="form-control form-control-lite" type="text" placeholder="Search projects...">
				</form>

				<div class="navbar-collapse collapse">
					<ul class="navbar-nav ms-auto">
						<li class="nav-item dropdown active">
							<a class="nav-link dropdown-toggle position-relative" href="#" id="messagesDropdown" data-bs-toggle="dropdown">
								<i class="align-middle fas fa-envelope-open"></i>
							</a>
							<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0" aria-labelledby="messagesDropdown">
								<div class="dropdown-menu-header">
									<div class="position-relative">
										4 New Messages
									</div>
								</div>
								<div class="list-group">
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<img src="img/avatars/avatar-5.jpg" class="avatar img-fluid rounded-circle" alt="Michelle Bilodeau">
											</div>
											<div class="col-10 ps-2">
												<div class="text-dark">Michelle Bilodeau</div>
												<div class="text-muted small mt-1">Nam pretium turpis et arcu. Duis arcu tortor.</div>
												<div class="text-muted small mt-1">5m ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<img src="img/avatars/avatar-3.jpg" class="avatar img-fluid rounded-circle" alt="Kathie Burton">
											</div>
											<div class="col-10 ps-2">
												<div class="text-dark">Kathie Burton</div>
												<div class="text-muted small mt-1">Pellentesque auctor neque nec urna.</div>
												<div class="text-muted small mt-1">30m ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<img src="img/avatars/avatar-2.jpg" class="avatar img-fluid rounded-circle" alt="Alexander Groves">
											</div>
											<div class="col-10 ps-2">
												<div class="text-dark">Alexander Groves</div>
												<div class="text-muted small mt-1">Curabitur ligula sapien euismod vitae.</div>
												<div class="text-muted small mt-1">2h ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<img src="img/avatars/avatar-4.jpg" class="avatar img-fluid rounded-circle" alt="Daisy Seger">
											</div>
											<div class="col-10 ps-2">
												<div class="text-dark">Daisy Seger</div>
												<div class="text-muted small mt-1">Aenean tellus metus, bibendum sed, posuere ac, mattis non.</div>
												<div class="text-muted small mt-1">5h ago</div>
											</div>
										</div>
									</a>
								</div>
								<div class="dropdown-menu-footer">
									<a href="#" class="text-muted">Show all messages</a>
								</div>
							</div>
						</li>
						<li class="nav-item dropdown ms-lg-2">
							<a class="nav-link dropdown-toggle position-relative" href="#" id="alertsDropdown" data-bs-toggle="dropdown">
								<i class="align-middle fas fa-bell"></i>
								<span class="indicator"></span>
							</a>
							<div class="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0" aria-labelledby="alertsDropdown">
								<div class="dropdown-menu-header">
									4 New Notifications
								</div>
								<div class="list-group">
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<i class="ms-1 text-danger fas fa-fw fa-bell"></i>
											</div>
											<div class="col-10">
												<div class="text-dark">Update completed</div>
												<div class="text-muted small mt-1">Restart server 12 to complete the update.</div>
												<div class="text-muted small mt-1">2h ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<i class="ms-1 text-warning fas fa-fw fa-envelope-open"></i>
											</div>
											<div class="col-10">
												<div class="text-dark">Lorem ipsum</div>
												<div class="text-muted small mt-1">Aliquam ex eros, imperdiet vulputate hendrerit et.</div>
												<div class="text-muted small mt-1">6h ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<i class="ms-1 text-primary fas fa-fw fa-building"></i>
											</div>
											<div class="col-10">
												<div class="text-dark">Login from ***********</div>
												<div class="text-muted small mt-1">8h ago</div>
											</div>
										</div>
									</a>
									<a href="#" class="list-group-item">
										<div class="row g-0 align-items-center">
											<div class="col-2">
												<i class="ms-1 text-success fas fa-fw fa-bell-slash"></i>
											</div>
											<div class="col-10">
												<div class="text-dark">New connection</div>
												<div class="text-muted small mt-1">Anna accepted your request.</div>
												<div class="text-muted small mt-1">12h ago</div>
											</div>
										</div>
									</a>
								</div>
								<div class="dropdown-menu-footer">
									<a href="#" class="text-muted">Show all notifications</a>
								</div>
							</div>
						</li>
						<li class="nav-item dropdown ms-lg-2">
							<a class="nav-link dropdown-toggle position-relative" href="#" id="userDropdown" data-bs-toggle="dropdown">
								<i class="align-middle fas fa-cog"></i>
							</a>
							<div class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
								<a class="dropdown-item" href="#"><i class="align-middle me-1 fas fa-fw fa-user"></i> View Profile</a>
								<a class="dropdown-item" href="#"><i class="align-middle me-1 fas fa-fw fa-comments"></i> Contacts</a>
								<a class="dropdown-item" href="#"><i class="align-middle me-1 fas fa-fw fa-chart-pie"></i> Analytics</a>
								<a class="dropdown-item" href="#"><i class="align-middle me-1 fas fa-fw fa-cogs"></i> Settings</a>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#"><i class="align-middle me-1 fas fa-fw fa-arrow-alt-circle-right"></i> Sign out</a>
							</div>
						</li>
					</ul>
				</div>
			</nav>
			<main class="content">
				<div class="container-fluid">

					<div class="header">
						<h1 class="header-title">
							Advanced Elements
						</h1>
						<nav aria-label="breadcrumb">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href='/dashboard-default'>Dashboard</a></li>
								<li class="breadcrumb-item"><a href="#">Forms</a></li>
								<li class="breadcrumb-item active" aria-current="page">Advanced Elements</li>
							</ol>
						</nav>
					</div>
					<div class="row">
						<div class="col-12 col-lg-5 col-xxl-6 d-flex">
							<div class="card flex-fill">
								<div class="card-header">
									<h5 class="card-title">Select2</h5>
									<h6 class="card-subtitle text-muted">The jQuery replacement for select boxes.</h6>
								</div>
								<div class="card-body">
									<div class="mb-3">
										<select class="form-control select2" data-bs-toggle="select2">
											<optgroup label="Alaskan/Hawaiian Time Zone">
												<option value="AK">Alaska</option>
												<option value="HI">Hawaii</option>
											</optgroup>
											<optgroup label="Pacific Time Zone">
												<option value="CA">California</option>
												<option value="NV">Nevada</option>
												<option value="OR">Oregon</option>
												<option value="WA">Washington</option>
											</optgroup>
											<optgroup label="Mountain Time Zone">
												<option value="AZ">Arizona</option>
												<option value="CO">Colorado</option>
												<option value="ID">Idaho</option>
												<option value="MT">Montana</option>
												<option value="NE">Nebraska</option>
												<option value="NM">New Mexico</option>
												<option value="ND">North Dakota</option>
												<option value="UT">Utah</option>
												<option value="WY">Wyoming</option>
											</optgroup>
											<optgroup label="Central Time Zone">
												<option value="AL">Alabama</option>
												<option value="AR">Arkansas</option>
												<option value="IL">Illinois</option>
												<option value="IA">Iowa</option>
												<option value="KS">Kansas</option>
												<option value="KY">Kentucky</option>
												<option value="LA">Louisiana</option>
												<option value="MN">Minnesota</option>
												<option value="MS">Mississippi</option>
												<option value="MO">Missouri</option>
												<option value="OK">Oklahoma</option>
												<option value="SD">South Dakota</option>
												<option value="TX">Texas</option>
												<option value="TN">Tennessee</option>
												<option value="WI">Wisconsin</option>
											</optgroup>
											<optgroup label="Eastern Time Zone">
												<option value="CT">Connecticut</option>
												<option value="DE">Delaware</option>
												<option value="FL">Florida</option>
												<option value="GA">Georgia</option>
												<option value="IN">Indiana</option>
												<option value="ME">Maine</option>
												<option value="MD">Maryland</option>
												<option value="MA">Massachusetts</option>
												<option value="MI">Michigan</option>
												<option value="NH">New Hampshire</option>
												<option value="NJ">New Jersey</option>
												<option value="NY">New York</option>
												<option value="NC">North Carolina</option>
												<option value="OH">Ohio</option>
												<option value="PA">Pennsylvania</option>
												<option value="RI">Rhode Island</option>
												<option value="SC">South Carolina</option>
												<option value="VT">Vermont</option>
												<option value="VA">Virginia</option>
												<option value="WV">West Virginia</option>
											</optgroup>
										</select>
									</div>

									<div class="mb-3">
										<select class="form-control select2" data-bs-toggle="select2" multiple>
											<optgroup label="Alaskan/Hawaiian Time Zone">
												<option value="AK">Alaska</option>
												<option value="HI">Hawaii</option>
											</optgroup>
											<optgroup label="Pacific Time Zone">
												<option value="CA">California</option>
												<option value="NV">Nevada</option>
												<option value="OR">Oregon</option>
												<option value="WA">Washington</option>
											</optgroup>
											<optgroup label="Mountain Time Zone">
												<option value="AZ">Arizona</option>
												<option value="CO">Colorado</option>
												<option value="ID">Idaho</option>
												<option value="MT">Montana</option>
												<option value="NE">Nebraska</option>
												<option value="NM">New Mexico</option>
												<option value="ND">North Dakota</option>
												<option value="UT">Utah</option>
												<option value="WY">Wyoming</option>
											</optgroup>
											<optgroup label="Central Time Zone">
												<option value="AL">Alabama</option>
												<option value="AR">Arkansas</option>
												<option value="IL">Illinois</option>
												<option value="IA">Iowa</option>
												<option value="KS">Kansas</option>
												<option value="KY">Kentucky</option>
												<option value="LA">Louisiana</option>
												<option value="MN">Minnesota</option>
												<option value="MS">Mississippi</option>
												<option value="MO">Missouri</option>
												<option value="OK">Oklahoma</option>
												<option value="SD">South Dakota</option>
												<option value="TX">Texas</option>
												<option value="TN">Tennessee</option>
												<option value="WI">Wisconsin</option>
											</optgroup>
											<optgroup label="Eastern Time Zone">
												<option value="CT">Connecticut</option>
												<option value="DE">Delaware</option>
												<option value="FL">Florida</option>
												<option value="GA">Georgia</option>
												<option value="IN">Indiana</option>
												<option value="ME">Maine</option>
												<option value="MD">Maryland</option>
												<option value="MA">Massachusetts</option>
												<option value="MI">Michigan</option>
												<option value="NH">New Hampshire</option>
												<option value="NJ">New Jersey</option>
												<option value="NY">New York</option>
												<option value="NC">North Carolina</option>
												<option value="OH">Ohio</option>
												<option value="PA">Pennsylvania</option>
												<option value="RI">Rhode Island</option>
												<option value="SC">South Carolina</option>
												<option value="VT">Vermont</option>
												<option value="VA">Virginia</option>
												<option value="WV">West Virginia</option>
											</optgroup>
										</select>
									</div>

									<div>
										<select class="form-control select2" data-bs-toggle="select2" multiple>
											<option value="one">First</option>
											<option value="two" disabled="disabled">Second (disabled)</option>
											<option value="three">Third</option>
										</select>
									</div>
								</div>
							</div>
						</div>

						<div class="col-12 col-lg-7 col-xxl-6 d-flex">
							<div class="card flex-fill">
								<div class="card-header">
									<h5 class="card-title">Date Range Picker</h5>
									<h6 class="card-subtitle text-muted">Component for choosing date ranges, dates and times.</h6>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-12 col-xl-4">
											<div class="mb-3">
												<label class="form-label">Date Range</label>
												<input class="form-control" type="text" name="daterange" value="01/01/2023 - 01/15/2023" />
											</div>
										</div>

										<div class="col-12 col-xl-8">
											<div class="mb-3">
												<label class="form-label">Date Range with Times</label>
												<input class="form-control" type="text" name="datetimes" />
											</div>
										</div>

										<div class="col-12 col-xl-4">
											<div class="mb-3 mb-xl-0">
												<label class="form-label">Single Date Picker</label>
												<input class="form-control" type="text" name="datesingle" />
											</div>
										</div>

										<div class="col-12 col-xl-8">
											<div class="mb-3 mb-xl-0">
												<label class="form-label">Predefined Date Ranges</label>
												<div id="reportrange" class="overflow-hidden form-control">
													<i class="far fa-calendar"></i>&nbsp;
													<span></span> <i class="fas fa-caret-down"></i>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<div class="card">
								<div class="card-header">
									<h5 class="card-title">Date Time Picker</h5>
									<h6 class="card-subtitle text-muted">Date and time picker designed to integrate into your Bootstrap project.</h6>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-12 col-xl-6">
											<div class="mb-3">
												<label class="form-label">Minimum Setup</label>
												<div class="input-group date" id="datetimepicker-minimum" data-target-input="nearest">
													<input type="text" class="form-control datetimepicker-input" data-target="#datetimepicker-minimum" />
													<div class="input-group-text" data-target="#datetimepicker-minimum" data-toggle="datetimepicker"><i
															class="fa fa-calendar"></i></div>
												</div>
											</div>
											<div class="mb-3">
												<label class="form-label">Time Only</label>
												<div class="input-group date" id="datetimepicker-time" data-target-input="nearest">
													<input type="text" class="form-control datetimepicker-input" data-target="#datetimepicker-time" />
													<div class="input-group-text" data-target="#datetimepicker-time" data-toggle="datetimepicker"><i
															class="fa fa-calendar"></i></div>
												</div>
											</div>
										</div>
										<div class="col-12 col-xl-6">
											<div class="mb-3">
												<label class="form-label">Date Only</label>
												<div class="input-group date" id="datetimepicker-date" data-target-input="nearest">
													<input type="text" class="form-control datetimepicker-input" data-target="#datetimepicker-date" />
													<div class="input-group-text" data-target="#datetimepicker-date" data-toggle="datetimepicker"><i
															class="fa fa-calendar"></i></div>
												</div>
											</div>

											<div class="mb-3">
												<label class="form-label">View Mode</label>
												<div class="input-group date" id="datetimepicker-view-mode" data-target-input="nearest">
													<input type="text" class="form-control datetimepicker-input" data-target="#datetimepicker-view-mode" />
													<div class="input-group-text" data-target="#datetimepicker-view-mode" data-toggle="datetimepicker"><i
															class="fa fa-calendar"></i></div>
												</div>
											</div>
										</div>
									</div>

								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<div class="card">
								<div class="card-header">
									<h5 class="card-title">Input Masks</h5>
									<h6 class="card-subtitle text-muted">jQuery Plugin to make masks on form fields.</h6>
								</div>
								<div class="card-body">
									<div class="row">
										<div class="col-12 col-lg-6">
											<div class="mb-3">
												<label>Date</label>
												<input type="text" class="form-control" data-mask="00/00/0000">
												<span class="font-13 text-muted">e.g "DD/MM/YYYY"</span>
											</div>
											<div class="mb-3">
												<label>Hour</label>
												<input type="text" class="form-control" data-mask="00:00:00">
												<span class="font-13 text-muted">e.g "HH:MM:SS"</span>
											</div>
											<div class="mb-3">
												<label>Date & Hour</label>
												<input type="text" class="form-control" data-mask="00/00/0000 00:00:00">
												<span class="font-13 text-muted">e.g "DD/MM/YYYY HH:MM:SS"</span>
											</div>
											<div class="mb-3">
												<label>ZIP Code</label>
												<input type="text" class="form-control" data-mask="00000-000">
												<span class="font-13 text-muted">e.g "xxxxx-xxx"</span>
											</div>
											<div class="mb-3">
												<label>Crazy Zip Code</label>
												<input type="text" class="form-control" data-mask="0-00-00-00">
												<span class="font-13 text-muted">e.g "x-xx-xx-xx"</span>
											</div>
											<div class="mb-3">
												<label>Money</label>
												<input type="text" class="form-control" data-mask="***************.000,00" data-reverse="true">
												<span class="font-13 text-muted">e.g "Your money"</span>
											</div>
											<div class="mb-3">
												<label>Money 2</label>
												<input type="text" class="form-control" data-mask="#.##0,00" data-reverse="true">
												<span class="font-13 text-muted">e.g "#.##0,00"</span>
											</div>
										</div>
										<div class="col-12 col-lg-6">
											<div class="mb-3">
												<label>Telephone</label>
												<input type="text" class="form-control" data-mask="0000-0000">
												<span class="font-13 text-muted">e.g "xxxx-xxxx"</span>
											</div>
											<div class="mb-3">
												<label>Telephone with Code Area</label>
												<input type="text" class="form-control" data-mask="(00) 0000-0000">
												<span class="font-13 text-muted">e.g "(xx) xxxx-xxxx"</span>
											</div>
											<div class="mb-3">
												<label>US Telephone</label>
												<input type="text" class="form-control" data-mask="(*************">
												<span class="font-13 text-muted">e.g "(xxx) xxx-xxxx"</span>
											</div>
											<div class="mb-3">
												<label>São Paulo Celphones</label>
												<input type="text" class="form-control" data-mask="(00) 00000-0000">
												<span class="font-13 text-muted">e.g "(xx) xxxxx-xxxx"</span>
											</div>
											<div class="mb-3">
												<label>CPF</label>
												<input type="text" class="form-control" data-mask="000.000.000-00" data-reverse="true">
												<span class="font-13 text-muted">e.g "xxx.xxx.xxxx-xx"</span>
											</div>
											<div class="mb-3">
												<label>CNPJ</label>
												<input type="text" class="form-control" data-mask="00.000.000/0000-00" data-reverse="true">
												<span class="font-13 text-muted">e.g "xx.xxx.xxx/xxxx-xx"</span>
											</div>
											<div class="mb-3">
												<label>IP Address</label>
												<input type="text" class="form-control" data-mask="***************" data-reverse="true">
												<span class="font-13 text-muted">e.g "xxx.xxx.xxx.xxx"</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
			<footer class="footer">
				<div class="container-fluid">
					<div class="row text-muted">
						<div class="col-8 text-start">
							<ul class="list-inline">
								<li class="list-inline-item">
									<a class="text-muted" href="#">Support</a>
								</li>
								<li class="list-inline-item">
									<a class="text-muted" href="#">Privacy</a>
								</li>
								<li class="list-inline-item">
									<a class="text-muted" href="#">Terms of Service</a>
								</li>
								<li class="list-inline-item">
									<a class="text-muted" href="#">Contact</a>
								</li>
							</ul>
						</div>
						<div class="col-4 text-end">
							<p class="mb-0">
								&copy; 2023 - <a class='text-muted' href='/dashboard-default'>Spark</a>
							</p>
						</div>
					</div>
				</div>
			</footer>
		</div>
	</div>

	<svg width="0" height="0" style="position:absolute">
		<defs>
			<symbol viewBox="0 0 512 512" id="ion-ios-pulse-strong">
				<path
					d="M448 273.001c-21.27 0-39.296 13.999-45.596 32.999h-38.857l-28.361-85.417a15.999 15.999 0 0 0-15.183-10.956c-.112 0-.224 0-.335.004a15.997 15.997 0 0 0-15.049 11.588l-44.484 155.262-52.353-314.108C206.535 54.893 200.333 48 192 48s-13.693 5.776-15.525 13.135L115.496 306H16v31.999h112c7.348 0 13.75-5.003 15.525-12.134l45.368-182.177 51.324 307.94c1.229 7.377 7.397 11.92 14.864 12.344.308.018.614.028.919.028 7.097 0 13.406-3.701 15.381-10.594l49.744-173.617 15.689 47.252A16.001 16.001 0 0 0 352 337.999h51.108C409.973 355.999 427.477 369 448 369c26.511 0 48-22.492 48-49 0-26.509-21.489-46.999-48-46.999z">
				</path>
			</symbol>
		</defs>
	</svg>
	<script src="js/app.js"></script>

	<script>
		document.addEventListener("DOMContentLoaded", function() {
			// Select2
			$(".select2").each(function() {
				$(this)
					.wrap("<div class=\"position-relative\"></div>")
					.select2({
						placeholder: "Select value",
						dropdownParent: $(this).parent()
					});
			})
			// Daterangepicker
			$("input[name=\"daterange\"]").daterangepicker({
				opens: "left"
			});
			$("input[name=\"datetimes\"]").daterangepicker({
				timePicker: true,
				opens: "left",
				startDate: moment().startOf("hour"),
				endDate: moment().startOf("hour").add(32, "hour"),
				locale: {
					format: "M/DD hh:mm A"
				}
			});
			$("input[name=\"datesingle\"]").daterangepicker({
				singleDatePicker: true,
				showDropdowns: true
			});
			var start = moment().subtract(29, "days");
			var end = moment();

			function cb(start, end) {
				$("#reportrange span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"));
			}
			$("#reportrange").daterangepicker({
				startDate: start,
				endDate: end,
				ranges: {
					"Today": [moment(), moment()],
					"Yesterday": [moment().subtract(1, "days"), moment().subtract(1, "days")],
					"Last 7 Days": [moment().subtract(6, "days"), moment()],
					"Last 30 Days": [moment().subtract(29, "days"), moment()],
					"This Month": [moment().startOf("month"), moment().endOf("month")],
					"Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
				}
			}, cb);
			cb(start, end);
			// Datetimepicker
			$('#datetimepicker-minimum').datetimepicker();
			$('#datetimepicker-view-mode').datetimepicker({
				viewMode: 'years'
			});
			$('#datetimepicker-time').datetimepicker({
				format: 'LT'
			});
			$('#datetimepicker-date').datetimepicker({
				format: 'L'
			});
		});
	</script>
</body>

</html>