# Spark Django Templates - Installation Guide

## Quick Start

### 1. Copy Templates to Your Django Project

Copy the `spark_django` folder to your Django project root directory.

### 2. Update Django Settings

Add the following to your `settings.py`:

```python
import os

# Templates Configuration
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.jinja2.Jinja2',
        'DIRS': [
            os.path.join(BASE_DIR, 'spark_django', 'templates'),
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'environment': 'myproject.jinja2.environment',
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# Static Files Configuration
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'spark_django', 'static'),
]

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
```

### 3. Create Jinja2 Environment (Optional)

Create `myproject/jinja2.py`:

```python
from django.contrib.staticfiles.storage import staticfiles_storage
from django.urls import reverse
from jinja2 import Environment

def environment(**options):
    env = Environment(**options)
    env.globals.update({
        'static': staticfiles_storage.url,
        'url': reverse,
    })
    return env
```

### 4. URL Configuration

Create URL patterns for your views. Example `urls.py`:

```python
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls', namespace='dashboard')),
    path('auth/', include('authentication.urls', namespace='auth')),
    path('pages/', include('pages.urls', namespace='pages')),
    path('ui/', include('ui.urls', namespace='ui')),
    path('forms/', include('forms.urls', namespace='forms')),
    path('tables/', include('tables.urls', namespace='tables')),
]
```

### 5. Context Processors (Optional)

Add custom context processors for navigation and notifications:

```python
# context_processors.py
def navigation_context(request):
    return {
        'unread_messages_count': 0,  # Replace with actual logic
        'unread_notifications_count': 0,  # Replace with actual logic
        'recent_messages': [],  # Replace with actual messages
        'recent_notifications': [],  # Replace with actual notifications
    }

# Add to settings.py TEMPLATES context_processors
'myproject.context_processors.navigation_context',
```

## Theme Configuration

The templates support multiple themes:
- Modern (default)
- Classic
- Dark  
- Light

To change themes, modify the CSS link in `base/base.html`:

```html
<link href="{% static 'css/modern.css' %}" rel="stylesheet" id="theme-css">
```

## Dependencies

The templates require:
- Django 3.2+
- Bootstrap 5
- jQuery
- Feather Icons
- Chart.js (for charts)
- DataTables (for advanced tables)

## Next Steps

1. Create your Django apps and views
2. Customize the templates to match your data models
3. Add your business logic
4. Configure authentication and permissions
5. Test the responsive design on different devices
