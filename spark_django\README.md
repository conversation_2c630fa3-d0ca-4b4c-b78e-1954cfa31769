# Spark Django Templates

This is a Django-compatible version of the Spark Bootstrap 5 Admin Dashboard template, refactored to use Jinja2 templating with proper component separation and reusability.

## Structure

```
spark_django/
├── templates/
│   ├── base/
│   │   ├── base.html              # Main base template
│   │   └── auth_base.html         # Base template for authentication pages
│   ├── components/
│   │   ├── sidebar.html           # Sidebar navigation component
│   │   ├── navbar.html            # Top navigation bar component
│   │   ├── footer.html            # Footer component
│   │   └── widgets/               # Reusable widget components
│   │       ├── card.html
│   │       ├── chart.html
│   │       └── table.html
│   ├── pages/
│   │   ├── dashboard/
│   │   │   ├── default.html
│   │   │   ├── analytics.html
│   │   │   └── ecommerce.html
│   │   ├── forms/
│   │   ├── tables/
│   │   ├── ui/
│   │   ├── auth/
│   │   └── misc/
│   └── partials/                  # Small reusable template parts
├── static/
│   ├── css/
│   ├── js/
│   ├── img/
│   └── fonts/
└── templatetags/                  # Custom Django template tags
```

## Features

- **Modular Design**: Components are separated for maximum reusability
- **Jinja2 Compatible**: Uses Jinja2 syntax for Django integration
- **Responsive**: Bootstrap 5 responsive design
- **Multiple Themes**: Support for modern, classic, dark, and light themes
- **Component Library**: Reusable widgets and components
- **Django Integration**: Proper static file handling and URL patterns

## Usage

1. Copy the `spark_django` folder to your Django project
2. Add the templates directory to your Django settings
3. Configure static files handling
4. Use the templates in your views

## Template Inheritance

All page templates extend from `base/base.html` which provides:
- Common HTML structure
- CSS and JavaScript includes
- Navigation components
- Block definitions for customization

## What's Included

### Templates Created
- ✅ Base templates (main layout + auth layout)
- ✅ Navigation components (sidebar + top navbar)
- ✅ Dashboard pages (default, analytics, e-commerce)
- ✅ Authentication pages (login, register, password reset)
- ✅ Form templates (basic elements, validation)
- ✅ UI components (alerts, buttons)
- ✅ Table templates (Bootstrap tables, DataTables)
- ✅ Reusable widgets (cards, charts, stat cards)

### Static Assets
- ✅ CSS files (modern, classic, dark, light themes)
- ✅ JavaScript files (app.js, settings.js)
- ✅ Font files (Font Awesome, Ionicons)
- ✅ Image assets (avatars, brands, screenshots)

### Documentation
- ✅ Installation guide
- ✅ Usage examples
- ✅ URL configuration examples
- ✅ View examples

## Quick Start

1. **Copy the templates**: Copy `spark_django` folder to your Django project
2. **Configure settings**: Update your `settings.py` (see `INSTALLATION.md`)
3. **Set up URLs**: Create URL patterns for your views (see `example_urls.py`)
4. **Create views**: Implement your views (see `example_views.py`)
5. **Customize**: Modify templates to match your data models

## Customization

Templates use Jinja2 blocks for easy customization:
- `{% block title %}` - Page title
- `{% block extra_css %}` - Additional CSS
- `{% block content %}` - Main page content
- `{% block extra_js %}` - Additional JavaScript

## Files to Review

- `INSTALLATION.md` - Detailed setup instructions
- `USAGE.md` - How to use templates and components
- `example_urls.py` - URL configuration examples
- `example_views.py` - View implementation examples
