# Spark Django Templates - Usage Guide

## Template Structure

### Base Templates
- `base/base.html` - Main layout with sidebar and navbar
- `base/auth_base.html` - Simplified layout for authentication pages

### Components
- `components/sidebar.html` - Left navigation sidebar
- `components/navbar.html` - Top navigation bar
- `components/footer.html` - Page footer
- `components/widgets/` - Reusable UI widgets

### Page Templates
- `pages/dashboard/` - Dashboard pages
- `pages/auth/` - Authentication pages
- `pages/forms/` - Form examples
- `pages/ui/` - UI component examples
- `pages/tables/` - Table examples

## Creating Views

### Dashboard View Example

```python
from django.shortcuts import render
from django.contrib.auth.decorators import login_required

@login_required
def dashboard_default(request):
    context = {
        'recent_projects': Project.objects.filter(user=request.user)[:5],
        'chart_labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
        'chart_data': [10, 20, 30, 40, 50],
        'unread_messages_count': 4,
        'unread_notifications_count': 2,
    }
    return render(request, 'pages/dashboard/default.html', context)
```

### Form View Example

```python
from django.shortcuts import render, redirect
from django.contrib import messages
from .forms import ContactForm

def contact_form(request):
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Form submitted successfully!')
            return redirect('contact_form')
    else:
        form = ContactForm()
    
    return render(request, 'pages/forms/contact.html', {'form': form})
```

## Using Widgets

### Stat Card Widget

```html
{% include 'components/widgets/stat_card.html' with title="Sales Today" value="2,562" icon="truck" trend="-2.65" trend_text="Less sales than usual" %}
```

### Chart Widget

```html
{% include 'components/widgets/chart.html' with title="Recent Movement" chart_id="my-chart" chart_type="line" %}
```

### Card Widget

```html
{% include 'components/widgets/card.html' with title="Card Title" content="<p>Card content goes here</p>" %}
```

## Customizing Navigation

### Adding Menu Items

Edit `components/sidebar.html` to add new menu items:

```html
<li class="sidebar-item {% if request.resolver_match.url_name == 'my_page' %}active{% endif %}">
    <a class='sidebar-link' href='{% url "my_app:my_page" %}'>
        <i class="align-middle me-2 fas fa-fw fa-star"></i>
        <span class="align-middle">My Page</span>
    </a>
</li>
```

### Dynamic Menu Highlighting

The sidebar automatically highlights active menu items based on:
- `request.resolver_match.namespace` - App namespace
- `request.resolver_match.url_name` - URL name

## Form Integration

### Django Forms

Templates work seamlessly with Django forms:

```html
<form method="post">
    {% csrf_token %}
    <div class="mb-3">
        <label class="form-label" for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
        {{ form.name }}
        {% if form.name.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.name.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### Form Validation

Use Bootstrap validation classes:
- `is-valid` - Valid field
- `is-invalid` - Invalid field
- `valid-feedback` - Success message
- `invalid-feedback` - Error message

## Data Tables

### Basic Table

```html
<table class="table table-striped">
    <thead>
        <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for user in users %}
        <tr>
            <td>{{ user.get_full_name }}</td>
            <td>{{ user.email }}</td>
            <td>
                <a href="{% url 'users:edit' user.id %}" class="btn btn-sm btn-primary">Edit</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

### DataTables Integration

Include DataTables CSS/JS and initialize:

```html
{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script>
$(document).ready(function() {
    $('#myTable').DataTable();
});
</script>
{% endblock %}
```

## Theming

### Switching Themes

Change the CSS file in `base/base.html`:

```html
<!-- Modern Theme (default) -->
<link href="{% static 'css/modern.css' %}" rel="stylesheet">

<!-- Dark Theme -->
<link href="{% static 'css/dark.css' %}" rel="stylesheet">

<!-- Light Theme -->
<link href="{% static 'css/light.css' %}" rel="stylesheet">

<!-- Classic Theme -->
<link href="{% static 'css/classic.css' %}" rel="stylesheet">
```

### Custom Styling

Add custom CSS in the `extra_css` block:

```html
{% block extra_css %}
<style>
.my-custom-class {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
```
