# Example URL configurations for Spark Django Templates

# Main project urls.py
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls', namespace='dashboard')),
    path('auth/', include('authentication.urls', namespace='auth')),
    path('pages/', include('pages.urls', namespace='pages')),
    path('ui/', include('ui.urls', namespace='ui')),
    path('forms/', include('forms.urls', namespace='forms')),
    path('tables/', include('tables.urls', namespace='tables')),
    path('charts/', include('charts.urls', namespace='charts')),
    path('maps/', include('maps.urls', namespace='maps')),
    path('icons/', include('icons.urls', namespace='icons')),
    path('docs/', include('docs.urls', namespace='docs')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# dashboard/urls.py
from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    path('', views.default, name='default'),
    path('analytics/', views.analytics, name='analytics'),
    path('ecommerce/', views.ecommerce, name='ecommerce'),
]

# authentication/urls.py
from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'auth'

urlpatterns = [
    path('login/', auth_views.LoginView.as_view(template_name='pages/auth/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('password-reset/', auth_views.PasswordResetView.as_view(template_name='pages/auth/password_reset.html'), name='password_reset'),
]

# pages/urls.py
from django.urls import path
from . import views

app_name = 'pages'

urlpatterns = [
    path('settings/', views.settings, name='settings'),
    path('clients/', views.clients, name='clients'),
    path('invoice/', views.invoice, name='invoice'),
    path('pricing/', views.pricing, name='pricing'),
    path('tasks/', views.tasks, name='tasks'),
    path('chat/', views.chat, name='chat'),
    path('blank/', views.blank, name='blank'),
    path('404/', views.page_404, name='404'),
    path('500/', views.page_500, name='500'),
]

# ui/urls.py
from django.urls import path
from . import views

app_name = 'ui'

urlpatterns = [
    path('alerts/', views.alerts, name='alerts'),
    path('buttons/', views.buttons, name='buttons'),
    path('cards/', views.cards, name='cards'),
    path('general/', views.general, name='general'),
    path('grid/', views.grid, name='grid'),
    path('modals/', views.modals, name='modals'),
    path('offcanvas/', views.offcanvas, name='offcanvas'),
    path('placeholders/', views.placeholders, name='placeholders'),
    path('notifications/', views.notifications, name='notifications'),
    path('tabs/', views.tabs, name='tabs'),
    path('typography/', views.typography, name='typography'),
]

# forms/urls.py
from django.urls import path
from . import views

app_name = 'forms'

urlpatterns = [
    path('layouts/', views.layouts, name='layouts'),
    path('basic-elements/', views.basic_elements, name='basic_elements'),
    path('advanced-elements/', views.advanced_elements, name='advanced_elements'),
    path('floating-labels/', views.floating_labels, name='floating_labels'),
    path('input-groups/', views.input_groups, name='input_groups'),
    path('editors/', views.editors, name='editors'),
    path('validation/', views.validation, name='validation'),
    path('wizard/', views.wizard, name='wizard'),
]

# tables/urls.py
from django.urls import path
from . import views

app_name = 'tables'

urlpatterns = [
    path('bootstrap/', views.bootstrap, name='bootstrap'),
]

# datatables/urls.py (separate app for DataTables)
from django.urls import path
from . import views

app_name = 'datatables'

urlpatterns = [
    path('responsive/', views.responsive, name='responsive'),
    path('buttons/', views.buttons, name='buttons'),
    path('column-search/', views.column_search, name='column_search'),
    path('fixed-header/', views.fixed_header, name='fixed_header'),
    path('multi/', views.multi, name='multi'),
    path('ajax/', views.ajax, name='ajax'),
]

# charts/urls.py
from django.urls import path
from . import views

app_name = 'charts'

urlpatterns = [
    path('chartjs/', views.chartjs, name='chartjs'),
    path('apexcharts/', views.apexcharts, name='apexcharts'),
]
