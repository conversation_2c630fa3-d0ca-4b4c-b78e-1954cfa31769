# Example views for Spark Django Templates

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login
from django.contrib import messages
from django.views.generic import CreateView
from django.contrib.auth.forms import UserCreationForm
from django.urls import reverse_lazy

# Dashboard Views
@login_required
def dashboard_default(request):
    context = {
        'recent_projects': [],  # Add your project model data
        'chart_labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        'chart_data': [12, 19, 3, 5, 2, 3],
        'unread_messages_count': 4,
        'unread_notifications_count': 2,
    }
    return render(request, 'pages/dashboard/default.html', context)

@login_required
def dashboard_analytics(request):
    context = {
        'analytics_labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        'analytics_visitors': [100, 120, 80, 150, 200, 180, 160],
        'analytics_pageviews': [200, 240, 160, 300, 400, 360, 320],
        'traffic_source_labels': ['Direct', 'Social', 'Email', 'Other'],
        'traffic_source_data': [55, 25, 15, 5],
        'top_pages': [
            {'url': '/dashboard/', 'views': 1234, 'bounce_rate': 32.5},
            {'url': '/products/', 'views': 987, 'bounce_rate': 28.1},
        ],
        'browser_stats': [
            {'name': 'Chrome', 'icon': 'chrome', 'sessions': 4500, 'percentage': 65},
            {'name': 'Firefox', 'icon': 'firefox', 'sessions': 1200, 'percentage': 18},
        ],
    }
    return render(request, 'pages/dashboard/analytics.html', context)

@login_required
def dashboard_ecommerce(request):
    context = {
        'sales_labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        'sales_revenue': [1200, 1900, 3000, 5000, 2000, 3000],
        'sales_orders': [12, 19, 30, 50, 20, 30],
        'top_products': [],  # Add your product model data
        'recent_orders': [],  # Add your order model data
        'category_labels': ['Electronics', 'Clothing', 'Books', 'Home'],
        'category_data': [40, 30, 20, 10],
        'inventory_items': [],  # Add your inventory model data
    }
    return render(request, 'pages/dashboard/ecommerce.html', context)

# Authentication Views
class RegisterView(CreateView):
    form_class = UserCreationForm
    template_name = 'pages/auth/register.html'
    success_url = reverse_lazy('dashboard:default')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        login(self.request, self.object)
        messages.success(self.request, 'Account created successfully!')
        return response

# UI Component Views
def ui_alerts(request):
    return render(request, 'pages/ui/alerts.html')

def ui_buttons(request):
    return render(request, 'pages/ui/buttons.html')

# Form Views
def forms_basic_elements(request):
    return render(request, 'pages/forms/basic_elements.html')

def forms_validation(request):
    if request.method == 'POST':
        # Handle form submission
        form_data = request.POST
        # Add your validation logic here
        if form_data.get('first_name') and form_data.get('last_name'):
            messages.success(request, 'Form submitted successfully!')
        else:
            messages.error(request, 'Please fill in all required fields.')
    
    return render(request, 'pages/forms/validation.html')

# Table Views
def tables_bootstrap(request):
    context = {
        'table_data': [
            {'first_name': 'John', 'last_name': 'Doe', 'handle': '@johndoe'},
            {'first_name': 'Jane', 'last_name': 'Smith', 'handle': '@janesmith'},
        ],
        'employees': [
            {
                'name': 'John Doe',
                'position': 'Software Engineer',
                'office': 'New York',
                'age': 28,
                'start_date': '2022-01-15',
                'salary': 75000
            },
        ],
        'users': [],  # Add your user model data
    }
    return render(request, 'pages/tables/bootstrap.html', context)

def tables_datatables(request):
    context = {
        'employees': [
            {
                'id': 1,
                'name': 'John Doe',
                'position': 'Software Engineer',
                'office': 'New York',
                'age': 28,
                'start_date': '2022-01-15',
                'salary': 75000
            },
        ],
        'users': [],  # Add your user model data
    }
    return render(request, 'pages/tables/datatables.html', context)

# Page Views
@login_required
def pages_settings(request):
    return render(request, 'pages/misc/settings.html')

@login_required
def pages_clients(request):
    return render(request, 'pages/misc/clients.html')

def pages_blank(request):
    return render(request, 'pages/misc/blank.html')

def page_404(request):
    return render(request, 'pages/misc/404.html', status=404)

def page_500(request):
    return render(request, 'pages/misc/500.html', status=500)

# Context processor for navigation
def navigation_context(request):
    """
    Add this to your TEMPLATES context_processors in settings.py
    """
    return {
        'unread_messages_count': 0,  # Replace with actual logic
        'unread_notifications_count': 0,  # Replace with actual logic
        'recent_messages': [],  # Replace with actual messages
        'recent_notifications': [],  # Replace with actual notifications
        'site_name': 'Your Site Name',
    }
