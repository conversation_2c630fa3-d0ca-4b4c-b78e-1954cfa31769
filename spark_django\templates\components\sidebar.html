{% load static %}
<nav id="sidebar" class="sidebar">
    <a class='sidebar-brand' href='{% url "dashboard:default" %}'>
        <svg>
            <use xlink:href="#ion-ios-pulse-strong"></use>
        </svg>
        Spark
    </a>
    <div class="sidebar-content">
        <div class="sidebar-user">
            <img src="{% static 'img/avatars/avatar.jpg' %}" class="img-fluid rounded-circle mb-2" alt="{{ user.get_full_name|default:user.username }}" />
            <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
            <small>{{ user.profile.title|default:"User" }}</small>
        </div>

        <ul class="sidebar-nav">
            <li class="sidebar-header">
                Main
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'dashboard' %}active{% endif %}">
                <a data-bs-target="#dashboards" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'dashboard' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-home"></i> <span class="align-middle">Dashboards</span>
                </a>
                <ul id="dashboards" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'dashboard' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'default' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "dashboard:default" %}'>Default</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'analytics' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "dashboard:analytics" %}'>Analytics</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'ecommerce' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "dashboard:ecommerce" %}'>E-commerce</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'pages' %}active{% endif %}">
                <a data-bs-target="#pages" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'pages' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-file"></i> <span class="align-middle">Pages</span>
                </a>
                <ul id="pages" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'pages' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'settings' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:settings" %}'>Settings</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'clients' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:clients" %}'>Clients 
                            <span class="sidebar-badge badge rounded-pill bg-primary">New</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'invoice' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:invoice" %}'>Invoice</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'pricing' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:pricing" %}'>Pricing</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'tasks' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:tasks" %}'>Tasks</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'chat' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:chat" %}'>Chat 
                            <span class="sidebar-badge badge rounded-pill bg-primary">New</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'blank' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:blank" %}'>Blank Page</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'auth' %}active{% endif %}">
                <a data-bs-target="#auth" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'auth' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-sign-in-alt"></i> <span class="align-middle">Auth</span>
                </a>
                <ul id="auth" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'auth' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'login' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "auth:login" %}'>Sign In</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'register' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "auth:register" %}'>Sign Up</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'password_reset' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "auth:password_reset" %}'>Reset Password</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == '404' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:404" %}'>404 Page</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == '500' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "pages:500" %}'>500 Page</a>
                    </li>
                </ul>
            </li>

            <li class="sidebar-header">
                Elements
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'ui' %}active{% endif %}">
                <a data-bs-target="#ui" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'ui' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-flask"></i> <span class="align-middle">User Interface</span>
                </a>
                <ul id="ui" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'ui' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'alerts' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:alerts" %}'>Alerts</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'buttons' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:buttons" %}'>Buttons</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'cards' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:cards" %}'>Cards</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'general' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:general" %}'>General</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'grid' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:grid" %}'>Grid</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'modals' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:modals" %}'>Modals</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'offcanvas' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:offcanvas" %}'>Offcanvas</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'placeholders' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:placeholders" %}'>Placeholders</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'notifications' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:notifications" %}'>Notifications</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'tabs' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:tabs" %}'>Tabs</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'typography' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "ui:typography" %}'>Typography</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'charts' %}active{% endif %}">
                <a data-bs-target="#charts" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'charts' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-chart-pie"></i> <span class="align-middle">Charts</span>
                    <span class="sidebar-badge badge rounded-pill bg-primary">New</span>
                </a>
                <ul id="charts" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'charts' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'chartjs' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "charts:chartjs" %}'>Chart.js</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'apexcharts' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "charts:apexcharts" %}'>ApexCharts</a>
                    </li>
                </ul>
            </li>

            <li class="sidebar-item {% if request.resolver_match.namespace == 'forms' %}active{% endif %}">
                <a data-bs-target="#forms" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'forms' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-check-square"></i> <span class="align-middle">Forms</span>
                </a>
                <ul id="forms" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'forms' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'layouts' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:layouts" %}'>Layouts</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'basic_elements' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:basic_elements" %}'>Basic Elements</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'advanced_elements' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:advanced_elements" %}'>Advanced Elements</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'floating_labels' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:floating_labels" %}'>Floating Labels</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'input_groups' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:input_groups" %}'>Input Groups</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'editors' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:editors" %}'>Editors</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'validation' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:validation" %}'>Validation</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'wizard' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "forms:wizard" %}'>Wizard</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.url_name == 'tables_bootstrap' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "tables:bootstrap" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-list"></i> <span class="align-middle">Tables</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'datatables' %}active{% endif %}">
                <a data-bs-target="#datatables" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'datatables' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-table"></i> <span class="align-middle">DataTables</span>
                </a>
                <ul id="datatables" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'datatables' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'responsive' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:responsive" %}'>Responsive Table</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'buttons' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:buttons" %}'>Table with Buttons</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'column_search' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:column_search" %}'>Column Search</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'fixed_header' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:fixed_header" %}'>Fixed Header</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'multi' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:multi" %}'>Multi Selection</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'ajax' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "datatables:ajax" %}'>Ajax Sourced Data</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'icons' %}active{% endif %}">
                <a data-bs-target="#icons" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'icons' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-heart"></i> <span class="align-middle">Icons</span>
                </a>
                <ul id="icons" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'icons' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'feather' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "icons:feather" %}'>Feather</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'ion' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "icons:ion" %}'>Ion Icons</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'font_awesome' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "icons:font_awesome" %}'>Font Awesome</a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.url_name == 'calendar' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "misc:calendar" %}'>
                    <i class="align-middle me-2 far fa-fw fa-calendar-alt"></i> <span class="align-middle">Calendar</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'maps' %}active{% endif %}">
                <a data-bs-target="#maps" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'maps' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-map-marker-alt"></i> <span class="align-middle">Maps</span>
                </a>
                <ul id="maps" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'maps' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'google' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "maps:google" %}'>Google Maps</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'vector' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "maps:vector" %}'>Vector Maps</a>
                    </li>
                </ul>
            </li>

            <li class="sidebar-header">
                Extras
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'docs' %}active{% endif %}">
                <a data-bs-target="#documentation" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'docs' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-book"></i> <span class="align-middle">Documentation</span>
                </a>
                <ul id="documentation" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'docs' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'getting_started' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "docs:getting_started" %}'>Getting Started</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'plugins' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "docs:plugins" %}'>Plugins</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'changelog' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "docs:changelog" %}'>Changelog</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
