{% comment %}
Statistics card widget component

Usage:
{% include 'components/widgets/stat_card.html' with title="Sales Today" value="2,562" icon="truck" trend="-2.65" trend_text="Less sales than usual" %}

Parameters:
- title: Card title
- value: Main statistic value
- icon: Feather icon name (without data-feather)
- icon_bg: Background color class for icon (default: bg-primary-dark)
- trend: Trend percentage (optional, can be positive or negative)
- trend_text: Description text for the trend (optional)
{% endcomment %}

<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col mt-0">
                <h5 class="card-title">{{ title }}</h5>
            </div>
            {% if icon %}
            <div class="col-auto">
                <div class="avatar">
                    <div class="avatar-title rounded-circle {{ icon_bg|default:'bg-primary-dark' }}">
                        <i class="align-middle" data-feather="{{ icon }}"></i>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        <h1 class="display-5 mt-1 mb-3">{{ value }}</h1>
        {% if trend or trend_text %}
        <div class="mb-0">
            {% if trend %}
            <span class="text-{% if trend|first == '-' %}danger{% else %}success{% endif %}">
                <i class="mdi mdi-arrow-{% if trend|first == '-' %}bottom-right{% else %}top-right{% endif %}"></i> {{ trend }}%
            </span>
            {% endif %}
            {% if trend_text %}
            {{ trend_text }}
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
