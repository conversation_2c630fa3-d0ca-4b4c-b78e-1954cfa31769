{% extends 'base/auth_base.html' %}
{% load static %}

{% block title %}Sign In - {{ block.super }}{% endblock %}

{% block content %}
<div class="text-center mt-4">
    <h1 class="h2">Welcome back!</h1>
    <p class="lead">
        Sign in to your account to continue
    </p>
</div>

<div class="card">
    <div class="card-body">
        <div class="m-sm-4">
            <div class="text-center">
                <svg class="avatar img-fluid rounded me-1" width="48" height="48">
                    <use xlink:href="#ion-ios-pulse-strong"></use>
                </svg>
            </div>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="form-label" for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.password.id_for_label }}">{{ form.password.label }}</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        Remember me next time
                    </label>
                </div>
                <div class="text-center mt-3">
                    <button type="submit" class="btn btn-lg btn-primary">Sign in</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="text-center mb-3">
    <a href="{% url 'auth:password_reset' %}" class="text-muted">Forgot Password?</a>
</div>

<div class="text-center mb-3">
    Don't have an account? <a href="{% url 'auth:register' %}">Sign up</a>
</div>
{% endblock %}
