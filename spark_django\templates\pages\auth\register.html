{% extends 'base/auth_base.html' %}
{% load static %}

{% block title %}Sign Up - {{ block.super }}{% endblock %}

{% block content %}
<div class="text-center mt-4">
    <h1 class="h2">Get started</h1>
    <p class="lead">
        Start creating the best possible user experience for you customers.
    </p>
</div>

<div class="card">
    <div class="card-body">
        <div class="m-sm-4">
            <div class="text-center">
                <svg class="avatar img-fluid rounded me-1" width="48" height="48">
                    <use xlink:href="#ion-ios-pulse-strong"></use>
                </svg>
            </div>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="form-label" for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.first_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.last_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.email.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password1.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <label class="form-label" for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password2.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                        I agree to the <a href="{% url 'legal:terms' %}" target="_blank">terms and conditions</a>
                    </label>
                </div>
                <div class="text-center mt-3">
                    <button type="submit" class="btn btn-lg btn-primary">Sign up</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="text-center mb-3">
    Already have an account? <a href="{% url 'auth:login' %}">Sign in</a>
</div>
{% endblock %}
