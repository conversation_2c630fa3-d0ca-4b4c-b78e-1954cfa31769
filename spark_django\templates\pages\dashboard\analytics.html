{% extends 'base/base.html' %}
{% load static %}

{% block title %}Analytics Dashboard - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Analytics Dashboard
    </h1>
    <p class="header-subtitle">Monitor your website performance and user engagement.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Visitors" value="14,212" icon="users" icon_bg="bg-primary" trend="5.25" trend_text="Since last week" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Page Views" value="2,562" icon="eye" icon_bg="bg-warning" trend="4.65" trend_text="Since last week" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Bounce Rate" value="21.3%" icon="trending-down" icon_bg="bg-success" trend="-2.25" trend_text="Since last week" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Session Duration" value="00:03:28" icon="clock" icon_bg="bg-danger" trend="1.15" trend_text="Since last week" %}
    </div>
</div>

<div class="row">
    <div class="col-xl-8">
        {% include 'components/widgets/chart.html' with title="Visitors Analytics" chart_id="chartjs-analytics-line" chart_type="line" header_actions='<div class="dropdown show"><a href="#" data-bs-toggle="dropdown" data-bs-display="static"><i class="align-middle" data-feather="more-vertical"></i></a><div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Last 7 days</a><a class="dropdown-item" href="#">Last 30 days</a><a class="dropdown-item" href="#">Last 90 days</a></div></div>' %}
    </div>
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Traffic Sources</h5>
            </div>
            <div class="card-body">
                <div class="chart chart-sm">
                    <canvas id="chartjs-analytics-pie"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Top Pages</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Page</th>
                                <th>Views</th>
                                <th>Bounce Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for page in top_pages %}
                            <tr>
                                <td>{{ page.url }}</td>
                                <td>{{ page.views|floatformat:0 }}</td>
                                <td>{{ page.bounce_rate }}%</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">No data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Browser Usage</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Browser</th>
                                <th>Sessions</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for browser in browser_stats %}
                            <tr>
                                <td>
                                    <i class="fab fa-{{ browser.icon }} me-2"></i>
                                    {{ browser.name }}
                                </td>
                                <td>{{ browser.sessions|floatformat:0 }}</td>
                                <td>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar" role="progressbar" style="width: {{ browser.percentage }}%" aria-valuenow="{{ browser.percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">No data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Real-time Activity</h5>
            </div>
            <div class="card-body">
                <div class="chart">
                    <canvas id="chartjs-analytics-realtime"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Analytics Line Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-analytics-line").getContext("2d");
        new Chart(ctx, {
            type: "line",
            data: {
                labels: {{ analytics_labels|safe }},
                datasets: [{
                    label: "Visitors",
                    fill: true,
                    backgroundColor: "rgba(51, 119, 255, 0.1)",
                    borderColor: window.theme.primary,
                    data: {{ analytics_visitors|safe }}
                }, {
                    label: "Page Views",
                    fill: true,
                    backgroundColor: "rgba(255, 193, 7, 0.1)",
                    borderColor: "#ffc107",
                    data: {{ analytics_pageviews|safe }}
                }]
            },
            options: {
                maintainAspectRatio: false,
                legend: {
                    display: true
                },
                tooltips: {
                    intersect: false
                },
                hover: {
                    intersect: true
                },
                plugins: {
                    filler: {
                        propagate: false
                    }
                }
            }
        });
    });

    // Traffic Sources Pie Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-analytics-pie").getContext("2d");
        new Chart(ctx, {
            type: "pie",
            data: {
                labels: {{ traffic_source_labels|safe }},
                datasets: [{
                    data: {{ traffic_source_data|safe }},
                    backgroundColor: [
                        window.theme.primary,
                        window.theme.warning,
                        window.theme.danger,
                        window.theme.success,
                        window.theme.info
                    ],
                    borderWidth: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: true
                }
            }
        });
    });

    // Real-time Activity Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-analytics-realtime").getContext("2d");
        new Chart(ctx, {
            type: "bar",
            data: {
                labels: {{ realtime_labels|safe }},
                datasets: [{
                    label: "Active Users",
                    backgroundColor: window.theme.primary,
                    borderColor: window.theme.primary,
                    data: {{ realtime_data|safe }}
                }]
            },
            options: {
                maintainAspectRatio: false,
                legend: {
                    display: false
                },
                scales: {
                    yAxes: [{
                        gridLines: {
                            display: false
                        },
                        stacked: false,
                        ticks: {
                            stepSize: 20
                        }
                    }],
                    xAxes: [{
                        stacked: false,
                        gridLines: {
                            color: "transparent"
                        }
                    }]
                }
            }
        });
    });
</script>
{% endblock %}
