{% extends 'base/base.html' %}
{% load static %}

{% block title %}Dashboard - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Welcome back, {{ user.first_name|default:user.username }}!
    </h1>
    <p class="header-subtitle">You have {{ unread_messages_count|default:0 }} new messages and {{ unread_notifications_count|default:0 }} new notifications.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-xl-6 col-xxl-7">
        {% include 'components/widgets/chart.html' with title="Recent Movement" chart_id="chartjs-dashboard-line" chart_type="line" header_actions='<a href="#" class="me-1"><i class="align-middle" data-feather="refresh-cw"></i></a><div class="d-inline-block dropdown show"><a href="#" data-bs-toggle="dropdown" data-bs-display="static"><i class="align-middle" data-feather="more-vertical"></i></a><div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Action</a><a class="dropdown-item" href="#">Another action</a><a class="dropdown-item" href="#">Something else here</a></div></div>' %}
    </div>

    <div class="col-xl-6 col-xxl-5 d-flex">
        <div class="w-100">
            <div class="row">
                <div class="col-sm-6">
                    {% include 'components/widgets/stat_card.html' with title="Sales Today" value="2,562" icon="truck" trend="-2.65" trend_text="Less sales than usual" %}
                    {% include 'components/widgets/stat_card.html' with title="Visitors Today" value="14,212" icon="users" trend="5.25" trend_text="More visitors than usual" %}
                </div>
                <div class="col-sm-6">
                    {% include 'components/widgets/stat_card.html' with title="Total Earnings" value="$21,300" icon="dollar-sign" trend="6.65" trend_text="More earnings than usual" %}
                    {% include 'components/widgets/stat_card.html' with title="Pending Orders" value="64" icon="shopping-cart" trend="-2.25" trend_text="Less orders than usual" %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 col-lg-8 d-flex">
        <div class="card flex-fill">
            <div class="card-header">
                <div class="card-actions float-end">
                    <div class="dropdown show">
                        <a href="#" data-bs-toggle="dropdown" data-bs-display="static">
                            <i class="align-middle" data-feather="more-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="#">Action</a>
                            <a class="dropdown-item" href="#">Another action</a>
                            <a class="dropdown-item" href="#">Something else here</a>
                        </div>
                    </div>
                </div>
                <h5 class="card-title mb-0">Latest Projects</h5>
            </div>
            <table id="datatables-dashboard-projects" class="table table-striped my-0">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th class="d-none d-xl-table-cell">Company</th>
                        <th class="d-none d-xl-table-cell">License</th>
                        <th>Progress</th>
                        <th class="d-none d-md-table-cell">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for project in recent_projects %}
                    <tr>
                        <td>{{ project.name }}</td>
                        <td class="d-none d-xl-table-cell">{{ project.company }}</td>
                        <td class="d-none d-xl-table-cell">{{ project.license }}</td>
                        <td>
                            <div class="progress progress-sm">
                                <div class="progress-bar" role="progressbar" style="width: {{ project.progress }}%" aria-valuenow="{{ project.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </td>
                        <td class="d-none d-md-table-cell">
                            <a href="{% url 'projects:detail' project.id %}" class="btn btn-light">View</a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">No projects found</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill w-100">
            <div class="card-header">
                <div class="card-actions float-end">
                    <div class="dropdown show">
                        <a href="#" data-bs-toggle="dropdown" data-bs-display="static">
                            <i class="align-middle" data-feather="more-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="#">Action</a>
                            <a class="dropdown-item" href="#">Another action</a>
                            <a class="dropdown-item" href="#">Something else here</a>
                        </div>
                    </div>
                </div>
                <h5 class="card-title mb-0">Monthly Sales</h5>
            </div>
            <div class="card-body d-flex">
                <div class="align-self-center w-100">
                    <div class="chart chart-lg">
                        <canvas id="chartjs-dashboard-bar"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 col-lg-8 d-flex">
        <div class="card flex-fill">
            <div class="card-header">
                <h5 class="card-title mb-0">World Map</h5>
            </div>
            <div class="card-body px-4">
                <div id="world_map" style="height:350px;"></div>
            </div>
        </div>
    </div>
    <div class="col-12 col-lg-4 d-flex">
        <div class="card flex-fill">
            <div class="card-header">
                <h5 class="card-title mb-0">Calendar</h5>
            </div>
            <div class="card-body d-flex">
                <div class="align-self-center w-100">
                    <div class="chart">
                        <div id="datetimepicker-dashboard"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Chart.js Dashboard Line Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-dashboard-line").getContext("2d");
        var gradientLight = ctx.createLinearGradient(0, 0, 0, 225);
        gradientLight.addColorStop(0, "rgba(215, 227, 244, 1)");
        gradientLight.addColorStop(1, "rgba(215, 227, 244, 0)");
        var gradientDark = ctx.createLinearGradient(0, 0, 0, 225);
        gradientDark.addColorStop(0, "rgba(51, 119, 255, 0.4)");
        gradientDark.addColorStop(1, "rgba(51, 119, 255, 0)");
        
        new Chart(ctx, {
            type: "line",
            data: {
                labels: {{ chart_labels|safe }},
                datasets: [{
                    label: "Sales ($)",
                    fill: true,
                    backgroundColor: window.theme.id === "light" ? gradientLight : gradientDark,
                    borderColor: window.theme.primary,
                    data: {{ chart_data|safe }}
                }]
            },
            options: {
                maintainAspectRatio: false,
                legend: {
                    display: false
                },
                tooltips: {
                    intersect: false
                },
                hover: {
                    intersect: true
                },
                plugins: {
                    filler: {
                        propagate: false
                    }
                },
                scales: {
                    xAxes: [{
                        reverse: true,
                        gridLines: {
                            color: "rgba(0,0,0,0.0)"
                        }
                    }],
                    yAxes: [{
                        ticks: {
                            stepSize: 1000
                        },
                        display: true,
                        borderDash: [3, 3],
                        gridLines: {
                            color: "rgba(0,0,0,0.0)",
                            fontColor: "#fff"
                        }
                    }]
                }
            }
        });
    });

    // DataTables
    $(function() {
        $('#datatables-dashboard-projects').DataTable({
            pageLength: 6,
            lengthChange: false,
            bFilter: false,
            autoWidth: false
        });
    });

    // Calendar
    $(function() {
        $('#datetimepicker-dashboard').datetimepicker({
            inline: true,
            sideBySide: false,
            format: 'L'
        });
    });
</script>
{% endblock %}
