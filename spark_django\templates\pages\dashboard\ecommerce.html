{% extends 'base/base.html' %}
{% load static %}

{% block title %}E-commerce Dashboard - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        E-commerce Dashboard
    </h1>
    <p class="header-subtitle">Track your online store performance and sales metrics.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Total Revenue" value="$21,300" icon="dollar-sign" icon_bg="bg-primary" trend="6.65" trend_text="Since last month" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Orders" value="64" icon="shopping-cart" icon_bg="bg-warning" trend="-2.25" trend_text="Since last month" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Customers" value="1,200" icon="users" icon_bg="bg-success" trend="12.5" trend_text="Since last month" %}
    </div>
    <div class="col-sm-6 col-xl-3">
        {% include 'components/widgets/stat_card.html' with title="Conversion Rate" value="3.2%" icon="trending-up" icon_bg="bg-danger" trend="0.8" trend_text="Since last month" %}
    </div>
</div>

<div class="row">
    <div class="col-xl-8">
        {% include 'components/widgets/chart.html' with title="Sales Overview" chart_id="chartjs-ecommerce-sales" chart_type="line" header_actions='<div class="dropdown show"><a href="#" data-bs-toggle="dropdown" data-bs-display="static"><i class="align-middle" data-feather="more-vertical"></i></a><div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Last 7 days</a><a class="dropdown-item" href="#">Last 30 days</a><a class="dropdown-item" href="#">Last 90 days</a></div></div>' %}
    </div>
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Top Products</h5>
            </div>
            <div class="card-body">
                {% for product in top_products %}
                <div class="d-flex align-items-center mb-3">
                    <img src="{% if product.image %}{{ product.image.url }}{% else %}{% static 'img/products/default.jpg' %}{% endif %}" 
                         class="avatar rounded me-3" alt="{{ product.name }}">
                    <div class="flex-grow-1">
                        <div class="fw-bold">{{ product.name }}</div>
                        <div class="text-muted small">${{ product.price }}</div>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">{{ product.sales_count }}</div>
                        <div class="text-muted small">sold</div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted">No products found</div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Orders</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td><a href="{% url 'orders:detail' order.id %}">#{{ order.id }}</a></td>
                                <td>{{ order.customer.get_full_name }}</td>
                                <td>${{ order.total_amount }}</td>
                                <td>
                                    <span class="badge bg-{% if order.status == 'completed' %}success{% elif order.status == 'pending' %}warning{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ order.created_at|date:"M d, Y" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">No orders found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Sales by Category</h5>
            </div>
            <div class="card-body">
                <div class="chart chart-sm">
                    <canvas id="chartjs-ecommerce-category"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xl-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Locations</h5>
            </div>
            <div class="card-body">
                <div id="world_map_ecommerce" style="height:300px;"></div>
            </div>
        </div>
    </div>
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Inventory Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in inventory_items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{% if item.product.image %}{{ item.product.image.url }}{% else %}{% static 'img/products/default.jpg' %}{% endif %}" 
                                             class="avatar avatar-sm rounded me-2" alt="{{ item.product.name }}">
                                        {{ item.product.name }}
                                    </div>
                                </td>
                                <td>{{ item.sku }}</td>
                                <td>{{ item.stock_quantity }}</td>
                                <td>
                                    <span class="badge bg-{% if item.stock_quantity > item.low_stock_threshold %}success{% elif item.stock_quantity > 0 %}warning{% else %}danger{% endif %}">
                                        {% if item.stock_quantity > item.low_stock_threshold %}In Stock{% elif item.stock_quantity > 0 %}Low Stock{% else %}Out of Stock{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <a href="{% url 'inventory:edit' item.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">No inventory items found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Sales Overview Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-ecommerce-sales").getContext("2d");
        new Chart(ctx, {
            type: "line",
            data: {
                labels: {{ sales_labels|safe }},
                datasets: [{
                    label: "Revenue",
                    fill: true,
                    backgroundColor: "rgba(51, 119, 255, 0.1)",
                    borderColor: window.theme.primary,
                    data: {{ sales_revenue|safe }}
                }, {
                    label: "Orders",
                    fill: true,
                    backgroundColor: "rgba(255, 193, 7, 0.1)",
                    borderColor: "#ffc107",
                    data: {{ sales_orders|safe }}
                }]
            },
            options: {
                maintainAspectRatio: false,
                legend: {
                    display: true
                },
                tooltips: {
                    intersect: false
                },
                hover: {
                    intersect: true
                },
                plugins: {
                    filler: {
                        propagate: false
                    }
                }
            }
        });
    });

    // Sales by Category Chart
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("chartjs-ecommerce-category").getContext("2d");
        new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: {{ category_labels|safe }},
                datasets: [{
                    data: {{ category_data|safe }},
                    backgroundColor: [
                        window.theme.primary,
                        window.theme.warning,
                        window.theme.danger,
                        window.theme.success,
                        window.theme.info
                    ],
                    borderWidth: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: true
                },
                cutoutPercentage: 75
            }
        });
    });
</script>
{% endblock %}
