{% extends 'base/base.html' %}
{% load static %}

{% block title %}Bootstrap Tables - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">Bootstrap Tables</h1>
    <p class="header-subtitle">Examples of tables with additional elements like buttons, checkboxes, icons, and more.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Default Table</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">First</th>
                            <th scope="col">Last</th>
                            <th scope="col">Handle</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in table_data %}
                        <tr>
                            <th scope="row">{{ forloop.counter }}</th>
                            <td>{{ item.first_name }}</td>
                            <td>{{ item.last_name }}</td>
                            <td>{{ item.handle }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center text-muted">No data available</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Striped Table</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th scope="col">Name</th>
                            <th scope="col">Position</th>
                            <th scope="col">Office</th>
                            <th scope="col">Age</th>
                            <th scope="col">Start date</th>
                            <th scope="col">Salary</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr>
                            <td>{{ employee.name }}</td>
                            <td>{{ employee.position }}</td>
                            <td>{{ employee.office }}</td>
                            <td>{{ employee.age }}</td>
                            <td>{{ employee.start_date|date:"M d, Y" }}</td>
                            <td>${{ employee.salary|floatformat:0 }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">No employees found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Table with Actions</h5>
            </div>
            <div class="card-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th scope="col">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </th>
                            <th scope="col">Avatar</th>
                            <th scope="col">Name</th>
                            <th scope="col">Email</th>
                            <th scope="col">Status</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <input class="form-check-input" type="checkbox" value="{{ user.id }}">
                            </td>
                            <td>
                                <img src="{% if user.avatar %}{{ user.avatar.url }}{% else %}{% static 'img/avatars/avatar.jpg' %}{% endif %}" 
                                     class="avatar img-fluid rounded-circle" alt="{{ user.get_full_name }}">
                            </td>
                            <td>{{ user.get_full_name }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                <span class="badge bg-{% if user.is_active %}success{% else %}secondary{% endif %}">
                                    {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </td>
                            <td>
                                <a href="{% url 'users:edit' user.id %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                <a href="{% url 'users:delete' user.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">No users found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Select all checkbox functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
</script>
{% endblock %}
