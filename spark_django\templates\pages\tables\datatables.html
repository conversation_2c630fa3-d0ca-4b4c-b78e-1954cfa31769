{% extends 'base/base.html' %}
{% load static %}

{% block title %}DataTables - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.6/css/buttons.bootstrap5.min.css">
{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">DataTables</h1>
    <p class="header-subtitle">Advanced tables with search, sorting, and pagination functionality.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Responsive DataTable</h5>
            </div>
            <div class="card-body">
                <table id="datatables-responsive" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Age</th>
                            <th>Start date</th>
                            <th>Salary</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr>
                            <td>{{ employee.name }}</td>
                            <td>{{ employee.position }}</td>
                            <td>{{ employee.office }}</td>
                            <td>{{ employee.age }}</td>
                            <td>{{ employee.start_date|date:"M d, Y" }}</td>
                            <td>${{ employee.salary|floatformat:0 }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'employees:view' employee.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="{% url 'employees:edit' employee.id %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                    <a href="{% url 'employees:delete' employee.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">DataTable with Buttons</h5>
            </div>
            <div class="card-body">
                <table id="datatables-buttons" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Department</th>
                            <th>Status</th>
                            <th>Join Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.get_full_name }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.department|default:"N/A" }}</td>
                            <td>
                                <span class="badge bg-{% if user.is_active %}success{% else %}secondary{% endif %}">
                                    {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </td>
                            <td>{{ user.date_joined|date:"M d, Y" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>

<script>
    $(document).ready(function() {
        // Responsive DataTable
        $('#datatables-responsive').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { targets: -1, orderable: false } // Disable sorting on Actions column
            ]
        });

        // DataTable with Buttons
        $('#datatables-buttons').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            pageLength: 25,
            order: [[0, 'asc']]
        });
    });
</script>
{% endblock %}
